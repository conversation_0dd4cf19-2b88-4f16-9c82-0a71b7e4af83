# تعليمات إعداد نظام إدارة المنتجات

## 🚀 خطوات التشغيل السريع

### 1. تأكد من تشغيل MySQL
```bash
# تأكد من تشغيل خادم MySQL
# في Windows: تشغيل XAMPP أو WAMP
# في Linux/Mac: 
sudo systemctl start mysql
```

### 2. إنشاء قاعدة البيانات
```sql
-- في MySQL Command Line أو phpMyAdmin
CREATE DATABASE droobhajer_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE droobhajer_db;

-- تشغيل ملف إنشاء الجداول
SOURCE droobhajer_db.sql;

-- تشغيل البيانات التجريبية
SOURCE sample_data.sql;
```

### 3. إعداد متغيرات البيئة
إنشاء ملف `.env.local` في جذر المشروع:
```env
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_mysql_password
DB_NAME=droobhajer_db
```

### 4. اختبار الاتصال
```bash
node test-db-connection.js
```

### 5. تشغيل المشروع
```bash
npm run dev
```

### 6. اختبار النظام
- **المنتجات**: `http://localhost:3000/admin/products`
- **الفئات الرئيسية**: `http://localhost:3000/admin/categories`
- **الفئات الفرعية**: `http://localhost:3000/admin/subcategories`

### 7. اختبار عمليات CRUD
```bash
# اختبار الفئات والفئات الفرعية
node test-categories-crud.js

# اختبار المنتجات مع جميع التفاصيل
node test-products-crud.js

# اختبار قاعدة البيانات
node test-db-connection.js
```

## 🔧 حل المشاكل الشائعة

### مشكلة: خطأ اتصال قاعدة البيانات
```
Error: connect ECONNREFUSED 127.0.0.1:3306
```
**الحل:**
1. تأكد من تشغيل MySQL
2. تحقق من بيانات الاتصال في `.env.local`

### مشكلة: Too many connections ⚠️ **شائعة**
```
Error: Too many connections
```
**الحل الفوري:**
1. **إعادة تشغيل MySQL في XAMPP:**
   - Stop MySQL → انتظار → Start MySQL
2. **أو في الخادم:**
   ```bash
   sudo systemctl restart mysql
   ```
3. **اختبار الحل:**
   ```bash
   node test-db-connection.js
   ```

### مشكلة: قاعدة البيانات غير موجودة
```
Error: Unknown database 'droobhajer_db'
```
**الحل:**
```sql
CREATE DATABASE droobhajer_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### مشكلة: الجداول غير موجودة
```
Error: Table 'droobhajer_db.products' doesn't exist
```
**الحل:**
```sql
USE droobhajer_db;
SOURCE droobhajer_db.sql;
```

### مشكلة: لا توجد بيانات
**الحل:**
```sql
USE droobhajer_db;
SOURCE sample_data.sql;
```

## 📊 التحقق من البيانات

### عرض الفئات:
```sql
SELECT * FROM categories;
```

### عرض المنتجات:
```sql
SELECT * FROM products;
```

### عرض منتج مع تفاصيله:
```sql
SELECT 
    p.*,
    GROUP_CONCAT(DISTINCT pi.image_url) as images,
    GROUP_CONCAT(DISTINCT pf.feature_text_ar) as features,
    GROUP_CONCAT(DISTINCT ps.spec_key_ar, ': ', ps.spec_value_ar) as specifications
FROM products p
LEFT JOIN product_images pi ON p.id = pi.product_id
LEFT JOIN product_features pf ON p.id = pf.product_id  
LEFT JOIN product_specifications ps ON p.id = ps.product_id
WHERE p.id = 'prod-1'
GROUP BY p.id;
```

## 🎯 نقاط الاختبار

### 1. **صفحة المنتجات**: `/admin/products`
   - عرض قائمة المنتجات مع الصور والمميزات والمواصفات
   - البحث والتصفية حسب الفئة
   - إضافة منتج جديد مع:
     * البيانات الأساسية (العنوان، الوصف، السعر، الفئة)
     * صور متعددة (حتى 5 صور)
     * مميزات متعددة بالعربية والإنجليزية
     * مواصفات متعددة بالعربية والإنجليزية
   - تعديل منتج موجود مع إمكانية تحديث جميع التفاصيل
   - حذف منتج مع جميع البيانات المرتبطة (صور، مميزات، مواصفات)
   - تفعيل/إلغاء تفعيل المنتج
   - جعل المنتج مميز/إلغاء التمييز

### 2. **صفحة الفئات الرئيسية**: `/admin/categories`
   - عرض قائمة الفئات الرئيسية
   - إضافة فئة رئيسية جديدة
   - تعديل فئة موجودة
   - تفعيل/إلغاء تفعيل فئة
   - حذف فئة (مع تحذير من حذف الفئات الفرعية)

### 3. **صفحة الفئات الفرعية**: `/admin/subcategories`
   - عرض قائمة الفئات الفرعية مع الصور
   - تصفية حسب الفئة الرئيسية
   - إضافة فئة فرعية جديدة مع الصورة والوصف بالإنجليزية والعربية
   - تعديل فئة فرعية موجودة
   - تفعيل/إلغاء تفعيل فئة فرعية
   - حذف فئة فرعية

### 4. **API Endpoints**:
   **المنتجات:**
   - `GET /api/admin/products` - جلب جميع المنتجات مع التفاصيل (صور، مميزات، مواصفات)
   - `GET /api/admin/products?id=xxx` - جلب منتج واحد مع جميع التفاصيل
   - `GET /api/admin/products?categoryId=xxx` - جلب المنتجات حسب الفئة
   - `GET /api/admin/products?subcategoryId=xxx` - جلب المنتجات حسب الفئة الفرعية
   - `POST /api/admin/products` - إضافة منتج مع جميع التفاصيل (4 جداول)
   - `PUT /api/admin/products?id=xxx` - تحديث منتج مع إمكانية تحديث جميع الجداول
   - `DELETE /api/admin/products?id=xxx` - حذف منتج مع جميع البيانات المرتبطة

   **الفئات الرئيسية:**
   - `GET /api/admin/categories` - جلب الفئات الرئيسية
   - `POST /api/admin/categories` - إضافة فئة رئيسية
   - `PUT /api/admin/categories?id=xxx` - تحديث فئة رئيسية
   - `DELETE /api/admin/categories?id=xxx` - حذف فئة رئيسية

   **الفئات الفرعية:**
   - `GET /api/admin/subcategories` - جلب الفئات الفرعية
   - `POST /api/admin/subcategories` - إضافة فئة فرعية
   - `PUT /api/admin/subcategories?id=xxx` - تحديث فئة فرعية
   - `DELETE /api/admin/subcategories?id=xxx` - حذف فئة فرعية

### 5. **هيكل قاعدة البيانات للمنتجات**:
   النظام يستخدم **4 جداول مترابطة** لكل منتج:

   **1. جدول `products`** - البيانات الأساسية:
   - العنوان بالعربية والإنجليزية
   - الوصف بالعربية والإنجليزية
   - السعر والسعر الأصلي
   - الفئة والفئة الفرعية
   - حالة التوفر والتفعيل والتمييز

   **2. جدول `product_images`** - صور المنتج:
   - صور متعددة لكل منتج
   - ترتيب الصور (sort_order)

   **3. جدول `product_features`** - مميزات المنتج:
   - مميزات متعددة بالعربية والإنجليزية
   - ترتيب المميزات

   **4. جدول `product_specifications`** - مواصفات المنتج:
   - مواصفات متعددة (مفتاح وقيمة)
   - كل مواصفة بالعربية والإنجليزية
   - ترتيب المواصفات

### 6. **اختبار البيانات المتكاملة**:
   - التأكد من ظهور الصور في المنتجات (جدول product_images)
   - التأكد من ظهور المميزات في المنتجات (جدول product_features)
   - التأكد من ظهور المواصفات في المنتجات (جدول product_specifications)
   - التأكد من ربط الفئات الفرعية بالفئات الرئيسية
   - التأكد من ربط المنتجات بالفئات والفئات الفرعية
   - اختبار حذف منتج وضمان حذف جميع البيانات المرتبطة

## 📝 ملاحظات مهمة

- تم إزالة المصادقة مؤقتاً للاختبار
- جميع العمليات تستخدم MySQL بدلاً من JSON
- البيانات التجريبية تحتوي على 4 منتجات كاملة
- النظام يدعم اللغتين العربية والإنجليزية

## 🔄 إعادة تفعيل المصادقة

عند الانتهاء من الاختبار، قم بإعادة تفعيل المصادقة في:
- `src/pages/api/admin/products.ts`
- `src/pages/api/admin/categories.ts`
- `src/pages/api/admin/subcategories.ts`

بإزالة التعليقات من كود `requireAdminAuth`.
