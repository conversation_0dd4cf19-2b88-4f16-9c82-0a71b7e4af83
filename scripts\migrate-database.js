const fs = require('fs');
const path = require('path');
const CryptoJS = require('crypto-js');

// إعدادات التشفير
const DB_ENCRYPTION_KEY = process.env.DB_ENCRYPTION_KEY || process.env.ENCRYPTION_KEY || 'your-database-encryption-key';

// مسارات الملفات
const ORIGINAL_DB_PATH = path.join(process.cwd(), 'src', 'data', 'database.json');
const ENCRYPTED_DB_PATH = path.join(process.cwd(), 'data', 'encrypted-database.enc');
const BACKUP_DB_PATH = path.join(process.cwd(), 'data', 'database-backup.json');

// إنشاء مجلد البيانات
function ensureDataDirectory() {
  const dataDir = path.dirname(ENCRYPTED_DB_PATH);
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
}

// تشفير البيانات
function encryptDatabase(data) {
  try {
    const jsonString = JSON.stringify(data, null, 2);
    const encrypted = CryptoJS.AES.encrypt(jsonString, DB_ENCRYPTION_KEY).toString();
    return encrypted;
  } catch (error) {
    console.error('Error encrypting database:', error);
    throw new Error('Failed to encrypt database');
  }
}

// ترحيل قاعدة البيانات
async function migrateDatabaseToEncrypted() {
  console.log('🔄 بدء ترحيل قاعدة البيانات إلى النظام المشفر...');
  
  try {
    // التحقق من وجود الملف الأصلي
    if (!fs.existsSync(ORIGINAL_DB_PATH)) {
      console.error('❌ ملف قاعدة البيانات الأصلي غير موجود:', ORIGINAL_DB_PATH);
      return;
    }
    
    ensureDataDirectory();
    
    // قراءة قاعدة البيانات الأصلية
    console.log('📖 قراءة قاعدة البيانات الأصلية...');
    const originalData = JSON.parse(fs.readFileSync(ORIGINAL_DB_PATH, 'utf8'));
    
    // إنشاء نسخة احتياطية
    console.log('💾 إنشاء نسخة احتياطية...');
    fs.writeFileSync(BACKUP_DB_PATH, JSON.stringify(originalData, null, 2));
    
    // إزالة البيانات الحساسة
    let cleanedData = { ...originalData };
    if (cleanedData.adminUser) {
      console.log('⚠️  إزالة بيانات المستخدم الإداري الحساسة...');
      delete cleanedData.adminUser;
    }
    
    // تشفير البيانات
    console.log('🔐 تشفير قاعدة البيانات...');
    const encryptedData = encryptDatabase(cleanedData);
    
    // حفظ البيانات المشفرة
    console.log('💾 حفظ قاعدة البيانات المشفرة...');
    fs.writeFileSync(ENCRYPTED_DB_PATH, encryptedData, 'utf8');
    
    // التحقق من سلامة البيانات المشفرة
    console.log('✅ التحقق من سلامة البيانات...');
    const decrypted = CryptoJS.AES.decrypt(encryptedData, DB_ENCRYPTION_KEY);
    const jsonString = decrypted.toString(CryptoJS.enc.Utf8);
    const verificationData = JSON.parse(jsonString);
    
    // مقارنة البيانات
    const originalKeys = Object.keys(cleanedData).sort();
    const verificationKeys = Object.keys(verificationData).sort();
    
    if (JSON.stringify(originalKeys) !== JSON.stringify(verificationKeys)) {
      throw new Error('Data verification failed - keys mismatch');
    }
    
    console.log('✅ تم ترحيل قاعدة البيانات بنجاح!');
    console.log('\n📊 إحصائيات الترحيل:');
    console.log(`📁 الملف الأصلي: ${ORIGINAL_DB_PATH}`);
    console.log(`🔐 الملف المشفر: ${ENCRYPTED_DB_PATH}`);
    console.log(`💾 النسخة الاحتياطية: ${BACKUP_DB_PATH}`);
    console.log(`📦 عدد الأقسام: ${Object.keys(cleanedData).length}`);
    
    if (cleanedData.categories) {
      console.log(`🏷️  عدد الفئات: ${cleanedData.categories.length}`);
    }
    if (cleanedData.products) {
      console.log(`📦 عدد المنتجات: ${cleanedData.products.length}`);
    }
    
    console.log('\n📝 الخطوات التالية:');
    console.log('1. تأكد من عمل التطبيق مع قاعدة البيانات المشفرة');
    console.log('2. اختبر جميع العمليات (قراءة، كتابة، تحديث)');
    console.log('3. احذف ملف قاعدة البيانات الأصلي بعد التأكد من عمل كل شيء');
    console.log('4. احتفظ بالنسخة الاحتياطية في مكان آمن');
    
  } catch (error) {
    console.error('❌ خطأ في ترحيل قاعدة البيانات:', error);
    
    // تنظيف الملفات في حالة الخطأ
    if (fs.existsSync(ENCRYPTED_DB_PATH)) {
      fs.unlinkSync(ENCRYPTED_DB_PATH);
      console.log('🧹 تم حذف الملف المشفر المعطوب');
    }
    
    throw error;
  }
}

// اختبار فك التشفير
function testDecryption() {
  console.log('\n🧪 اختبار فك التشفير...');
  
  try {
    if (!fs.existsSync(ENCRYPTED_DB_PATH)) {
      console.error('❌ الملف المشفر غير موجود');
      return false;
    }
    
    const encryptedData = fs.readFileSync(ENCRYPTED_DB_PATH, 'utf8');
    const decrypted = CryptoJS.AES.decrypt(encryptedData, DB_ENCRYPTION_KEY);
    const jsonString = decrypted.toString(CryptoJS.enc.Utf8);
    
    if (!jsonString) {
      console.error('❌ فشل في فك التشفير - مفتاح خاطئ أو بيانات معطوبة');
      return false;
    }
    
    const data = JSON.parse(jsonString);
    console.log('✅ اختبار فك التشفير نجح');
    console.log(`📊 تم فك تشفير ${Object.keys(data).length} قسم`);
    
    return true;
  } catch (error) {
    console.error('❌ خطأ في اختبار فك التشفير:', error);
    return false;
  }
}

// تشغيل الترحيل
if (require.main === module) {
  migrateDatabaseToEncrypted()
    .then(() => {
      console.log('\n🎉 انتهى ترحيل قاعدة البيانات!');
      
      // اختبار فك التشفير
      if (testDecryption()) {
        console.log('\n✅ جميع الاختبارات نجحت - قاعدة البيانات جاهزة للاستخدام');
      } else {
        console.log('\n❌ فشل في اختبار فك التشفير - يرجى المراجعة');
      }
    })
    .catch((error) => {
      console.error('\n💥 فشل في ترحيل قاعدة البيانات:', error);
      process.exit(1);
    });
}

module.exports = { migrateDatabaseToEncrypted, testDecryption };
