const bcrypt = require('bcryptjs');

// دالة لتشفير كلمة المرور
async function hashPassword(password) {
  try {
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);
    console.log('كلمة المرور الأصلية:', password);
    console.log('كلمة المرور المشفرة:', hashedPassword);
    return hashedPassword;
  } catch (error) {
    console.error('خطأ في تشفير كلمة المرور:', error);
  }
}

// دالة للتحقق من كلمة المرور
async function verifyPassword(password, hashedPassword) {
  try {
    const isValid = await bcrypt.compare(password, hashedPassword);
    console.log('كلمة المرور صحيحة:', isValid);
    return isValid;
  } catch (error) {
    console.error('خطأ في التحقق من كلمة المرور:', error);
  }
}

// تشفير كلمة المرور الافتراضية
const defaultPassword = 'DroobHajer@2024!SecureAdminPassword#VeryLong&Complex';

console.log('🔐 أداة تشفير كلمة المرور');
console.log('================================');

hashPassword(defaultPassword).then(hashedPassword => {
  console.log('\n✅ تم تشفير كلمة المرور بنجاح!');
  console.log('\nيمكنك نسخ كلمة المرور المشفرة أعلاه واستخدامها في قاعدة البيانات.');
  
  // التحقق من صحة التشفير
  console.log('\n🔍 التحقق من صحة التشفير...');
  verifyPassword(defaultPassword, hashedPassword);
});

// إذا تم تمرير كلمة مرور كمعامل
const customPassword = process.argv[2];
if (customPassword) {
  console.log('\n🔐 تشفير كلمة المرور المخصصة...');
  hashPassword(customPassword);
}

console.log('\n📝 طريقة الاستخدام:');
console.log('node src/scripts/hash-password.js [كلمة_المرور_الجديدة]');
console.log('\nمثال:');
console.log('node src/scripts/hash-password.js "MyNewPassword123!"');
