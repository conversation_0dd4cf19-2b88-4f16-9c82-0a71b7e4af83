const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// مسارات المجلدات
const OLD_UPLOADS_DIR = path.join(process.cwd(), 'public', 'uploads');
const NEW_UPLOADS_DIR = path.join(process.cwd(), 'secure-uploads');
const MIGRATION_LOG = path.join(process.cwd(), 'migration-log.json');

// أنواع الملفات المسموحة
const ALLOWED_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.webp', '.gif'];

// إنشاء مجلد التخزين الآمن
function ensureSecureUploadsDir() {
  if (!fs.existsSync(NEW_UPLOADS_DIR)) {
    fs.mkdirSync(NEW_UPLOADS_DIR, { recursive: true });
  }
}

// إنشاء اسم ملف آمن
function generateSecureFilename(originalFilename) {
  const ext = path.extname(originalFilename).toLowerCase();
  const timestamp = Date.now();
  const randomBytes = crypto.randomBytes(16).toString('hex');
  return `${timestamp}_${randomBytes}${ext}`;
}

// فحص نوع الملف
function isValidImageFile(filePath) {
  try {
    const buffer = fs.readFileSync(filePath);
    
    // فحص Magic Numbers
    const magicNumbers = {
      'jpeg': [0xFF, 0xD8, 0xFF],
      'png': [0x89, 0x50, 0x4E, 0x47],
      'gif': [0x47, 0x49, 0x46],
      'webp': [0x52, 0x49, 0x46, 0x46]
    };
    
    for (const [type, magic] of Object.entries(magicNumbers)) {
      const matches = magic.every((byte, index) => buffer[index] === byte);
      if (matches) {
        return true;
      }
    }
    
    return false;
  } catch (error) {
    console.error('Error validating file:', error);
    return false;
  }
}

// ترحيل الملفات
async function migrateUploads() {
  console.log('🚀 بدء ترحيل الملفات إلى النظام الآمن...');
  
  // التحقق من وجود المجلد القديم
  if (!fs.existsSync(OLD_UPLOADS_DIR)) {
    console.log('❌ مجلد uploads القديم غير موجود');
    return;
  }
  
  ensureSecureUploadsDir();
  
  const migrationMap = {};
  const errors = [];
  let successCount = 0;
  let skipCount = 0;
  
  try {
    const files = fs.readdirSync(OLD_UPLOADS_DIR);
    console.log(`📁 تم العثور على ${files.length} ملف`);
    
    for (const filename of files) {
      const oldFilePath = path.join(OLD_UPLOADS_DIR, filename);
      const stats = fs.statSync(oldFilePath);
      
      // تجاهل المجلدات
      if (stats.isDirectory()) {
        continue;
      }
      
      // فحص امتداد الملف
      const ext = path.extname(filename).toLowerCase();
      if (!ALLOWED_EXTENSIONS.includes(ext)) {
        console.log(`⚠️  تجاهل ملف بامتداد غير مدعوم: ${filename}`);
        skipCount++;
        continue;
      }
      
      // فحص نوع الملف
      if (!isValidImageFile(oldFilePath)) {
        console.log(`⚠️  تجاهل ملف غير صالح: ${filename}`);
        errors.push(`ملف غير صالح: ${filename}`);
        skipCount++;
        continue;
      }
      
      try {
        // إنشاء اسم ملف آمن جديد
        const newFilename = generateSecureFilename(filename);
        const newFilePath = path.join(NEW_UPLOADS_DIR, newFilename);
        
        // نسخ الملف
        fs.copyFileSync(oldFilePath, newFilePath);
        
        // حفظ الخريطة للترحيل
        migrationMap[`/uploads/${filename}`] = `/api/files/${newFilename}`;
        
        console.log(`✅ تم ترحيل: ${filename} -> ${newFilename}`);
        successCount++;
        
      } catch (error) {
        console.error(`❌ خطأ في ترحيل ${filename}:`, error);
        errors.push(`خطأ في ترحيل ${filename}: ${error.message}`);
      }
    }
    
    // حفظ خريطة الترحيل
    fs.writeFileSync(MIGRATION_LOG, JSON.stringify({
      timestamp: new Date().toISOString(),
      migrationMap,
      stats: {
        total: files.length,
        success: successCount,
        skipped: skipCount,
        errors: errors.length
      },
      errors
    }, null, 2));
    
    console.log('\n📊 نتائج الترحيل:');
    console.log(`✅ تم ترحيل: ${successCount} ملف`);
    console.log(`⚠️  تم تجاهل: ${skipCount} ملف`);
    console.log(`❌ أخطاء: ${errors.length} ملف`);
    console.log(`📄 تم حفظ سجل الترحيل في: ${MIGRATION_LOG}`);
    
    if (errors.length > 0) {
      console.log('\n❌ الأخطاء:');
      errors.forEach(error => console.log(`  - ${error}`));
    }
    
  } catch (error) {
    console.error('❌ خطأ عام في الترحيل:', error);
  }
}

// تشغيل الترحيل
if (require.main === module) {
  migrateUploads().then(() => {
    console.log('\n🎉 انتهى الترحيل!');
    console.log('\n📝 الخطوات التالية:');
    console.log('1. تحقق من الملفات في مجلد secure-uploads');
    console.log('2. اختبر الوصول للملفات عبر /api/files/[filename]');
    console.log('3. حدث قاعدة البيانات لتستخدم الروابط الجديدة');
    console.log('4. احذف مجلد public/uploads بعد التأكد من عمل كل شيء');
  });
}

module.exports = { migrateUploads };
