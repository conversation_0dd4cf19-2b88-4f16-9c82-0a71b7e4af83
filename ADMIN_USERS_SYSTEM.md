# نظام إدارة المستخدمين - MySQL

تم تطوير نظام شامل لإدارة المستخدمين في لوحة التحكم باستخدام قاعدة البيانات MySQL بدلاً من ملفات JSON.

## الميزات الجديدة

### 🔐 نظام المصادقة المحدث
- تسجيل الدخول باستخدام قاعدة البيانات MySQL
- تشفير كلمات المرور باستخدام bcrypt
- JWT tokens للمصادقة الآمنة
- إدارة الجلسات المحسنة

### 👥 إدارة المستخدمين
- إضافة مستخدمين جدد من لوحة التحكم
- عرض قائمة جميع المستخدمين
- تحديث بيانات المستخدمين
- تغيير كلمات المرور
- إحصائيات المستخدمين

### 🎨 واجهة المستخدم المحسنة
- مودال تفاعلي لإضافة المستخدمين
- عرض اسم المستخدم الحقيقي في الهيدر
- صفحة منفصلة لإدارة المستخدمين
- فلترة وبحث في المستخدمين

## هيكل قاعدة البيانات

### جدول `admins`
```sql
CREATE TABLE `admins` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password_hash` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## API Endpoints

### المصادقة
- `POST /api/auth/admin-login` - تسجيل الدخول
- `GET /api/admin/users/me` - الحصول على بيانات المستخدم الحالي

### إدارة المستخدمين
- `GET /api/admin/users/list` - جلب قائمة المستخدمين
- `POST /api/admin/users/create` - إضافة مستخدم جديد
- `PUT /api/admin/users/update` - تحديث بيانات المستخدم
- `PUT /api/admin/users/change-password` - تغيير كلمة المرور

## الملفات المضافة/المحدثة

### API Endpoints
- `src/pages/api/auth/admin-login.ts` - تسجيل الدخول
- `src/pages/api/admin/users/me.ts` - بيانات المستخدم الحالي
- `src/pages/api/admin/users/list.ts` - قائمة المستخدمين
- `src/pages/api/admin/users/create.ts` - إضافة مستخدم
- `src/pages/api/admin/users/update.ts` - تحديث المستخدم
- `src/pages/api/admin/users/change-password.ts` - تغيير كلمة المرور

### المكونات
- `src/components/admin/AddUserModal.tsx` - مودال إضافة مستخدم

### الصفحات
- `src/pages/admin/users.tsx` - صفحة إدارة المستخدمين
- `src/pages/admin/account.tsx` - محدثة لاستخدام MySQL

### الملفات المحدثة
- `src/components/admin/AdminLayout.tsx` - عرض اسم المستخدم الحقيقي
- `src/pages/admin/login.tsx` - استخدام API الجديد

### Scripts
- `scripts/create-default-admin.js` - إنشاء مستخدم افتراضي

## التثبيت والإعداد

### 1. إنشاء المستخدم الافتراضي
```bash
node scripts/create-default-admin.js
```

### 2. بيانات تسجيل الدخول الافتراضية
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123
- **البريد الإلكتروني:** <EMAIL>

### 3. الوصول للنظام
- انتقل إلى `/admin/login`
- استخدم بيانات تسجيل الدخول الافتراضية
- غير كلمة المرور من إعدادات الحساب

## الصفحات المتاحة

### إدارة المستخدمين
- `/admin/users` - صفحة إدارة المستخدمين الجديدة
- `/admin/account` - إعدادات الحساب (محدثة)

### الميزات في صفحة إدارة المستخدمين
- عرض جميع المستخدمين في جدول
- إضافة مستخدمين جدد
- البحث والفلترة
- إحصائيات المستخدمين
- عرض آخر تسجيل دخول

### الميزات في إعدادات الحساب
- تحديث الملف الشخصي
- تغيير كلمة المرور
- إدارة المستخدمين (تبويب جديد)
- سجل النشاط

## الأمان

### تشفير كلمات المرور
- استخدام bcrypt مع 12 rounds
- عدم تخزين كلمات المرور الخام
- التحقق الآمن من كلمات المرور

### JWT Tokens
- انتهاء صلاحية خلال 24 ساعة
- تخزين آمن في localStorage و cookies
- التحقق من الصلاحية في كل طلب

### التحقق من البيانات
- التحقق من صحة البريد الإلكتروني
- التحقق من قوة كلمة المرور
- منع التكرار في أسماء المستخدمين والإيميلات

## الاستخدام

### إضافة مستخدم جديد
1. انتقل إلى `/admin/users` أو `/admin/account`
2. اضغط على "إضافة مستخدم جديد"
3. املأ البيانات المطلوبة
4. اضغط "إنشاء المستخدم"

### تغيير كلمة المرور
1. انتقل إلى `/admin/account`
2. اختر تبويب "كلمة المرور"
3. أدخل كلمة المرور الحالية والجديدة
4. اضغط "حفظ التغييرات"

### عرض المستخدمين
1. انتقل إلى `/admin/users`
2. استخدم البحث والفلترة حسب الحاجة
3. عرض تفاصيل كل مستخدم

## ملاحظات مهمة

- ⚠️ تأكد من تغيير كلمة المرور الافتراضية فوراً
- 🔒 جميع كلمات المرور مشفرة ولا يمكن استرجاعها
- 📧 تأكد من استخدام إيميلات صحيحة للمستخدمين
- 🔄 يتم تحديث وقت آخر تسجيل دخول تلقائياً
- 🗑️ الحذف الناعم متاح (deleted_at field)

## الدعم والصيانة

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.
