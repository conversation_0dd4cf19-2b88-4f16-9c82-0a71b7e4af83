#!/usr/bin/env node

/**
 * سكريبت تثبيت نظام الجلسات
 * يقوم بتثبيت المكتبات المطلوبة وإعداد النظام
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 بدء تثبيت نظام الجلسات...\n');

// 1. تثبيت المكتبات المطلوبة
console.log('📦 تثبيت المكتبات المطلوبة...');
try {
  execSync('npm install uuid', { stdio: 'inherit' });
  console.log('✅ تم تثبيت uuid بنجاح\n');
} catch (error) {
  console.error('❌ فشل في تثبيت المكتبات:', error.message);
  process.exit(1);
}

// 2. التحقق من وجود الملفات المطلوبة
console.log('🔍 التحقق من الملفات المطلوبة...');

const requiredFiles = [
  'src/lib/browser-session.ts',
  'src/lib/session-cart.ts',
  'app/[locale]/test-sessions/page.tsx'
];

let allFilesExist = true;
requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - غير موجود`);
    allFilesExist = false;
  }
});

if (!allFilesExist) {
  console.error('\n❌ بعض الملفات المطلوبة غير موجودة. تأكد من تشغيل السكريبت من المجلد الجذر للمشروع.');
  process.exit(1);
}

// 3. إنشاء ملف types إضافي إذا لم يكن موجوداً
console.log('\n📝 إنشاء ملفات الأنواع...');
const typesDir = 'src/types';
if (!fs.existsSync(typesDir)) {
  fs.mkdirSync(typesDir, { recursive: true });
}

const sessionTypesContent = `// أنواع البيانات لنظام الجلسات
export interface SessionInfo {
  sessionId: string;
  keysCount: number;
  keys: string[];
}

export interface CartItem {
  id: string;
  title: string;
  titleAr?: string;
  image: string;
  price: number;
  quantity: number;
}

export interface CartSummary {
  items: CartItem[];
  totalItems: number;
  totalAmount: number;
}
`;

fs.writeFileSync(path.join(typesDir, 'session.ts'), sessionTypesContent);
console.log('✅ تم إنشاء src/types/session.ts');

// 4. إنشاء ملف README للنظام
console.log('\n📚 إنشاء دليل الاستخدام...');
const readmeContent = `# نظام الجلسات المتعددة

## نظرة عامة
هذا النظام يسمح لعدة مستخدمين باستخدام الموقع في نفس الوقت دون تعارض في البيانات.

## كيف يعمل
- كل مستخدم يحصل على \`Session ID\` فريد عند دخوله الموقع
- البيانات تُحفظ في \`localStorage\` مرتبطة بمعرف الجلسة
- كل مستخدم يرى بياناته الخاصة فقط

## الملفات الرئيسية

### \`src/lib/browser-session.ts\`
- إدارة الجلسات في المتصفح
- إنشاء وإدارة معرفات الجلسات
- حفظ واسترجاع البيانات

### \`src/lib/session-cart.ts\`
- إدارة عربة التسوق المرتبطة بالجلسة
- دوال إضافة وحذف وتحديث المنتجات
- Hook React للاستخدام السهل

### \`middleware.ts\`
- إنشاء جلسة تلقائياً لكل زائر جديد
- حفظ معرف الجلسة في الكوكيز

## الاستخدام

### في المكونات
\`\`\`typescript
import { useCart } from '../lib/session-cart';

function MyComponent() {
  const { cart, addToCart, removeFromCart, total } = useCart();
  
  // استخدام العربة...
}
\`\`\`

### إضافة منتج
\`\`\`typescript
import { addToCart } from '../lib/session-cart';

addToCart({
  id: 'product-1',
  title: 'منتج تجريبي',
  image: '/image.jpg',
  price: 100
});
\`\`\`

## صفحة الاختبار
زر \`/test-sessions\` لاختبار النظام مع عدة مستخدمين.

## المزايا
- ✅ لا يحتاج قاعدة بيانات
- ✅ سريع ومباشر
- ✅ يعمل مع عدة مستخدمين
- ✅ بيانات منفصلة لكل مستخدم
- ✅ تنظيف تلقائي للبيانات القديمة

## ملاحظات
- البيانات تُحفظ في المتصفح فقط
- تختفي البيانات عند مسح الكوكيز/localStorage
- مناسب للمواقع التي لا تحتاج حفظ دائم للبيانات
`;

fs.writeFileSync('SESSION_SYSTEM_README.md', readmeContent);
console.log('✅ تم إنشاء SESSION_SYSTEM_README.md');

// 5. التحقق من package.json
console.log('\n🔧 التحقق من إعدادات المشروع...');
const packageJsonPath = 'package.json';
if (fs.existsSync(packageJsonPath)) {
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  // التحقق من وجود uuid
  const hasUuid = packageJson.dependencies?.uuid || packageJson.devDependencies?.uuid;
  if (hasUuid) {
    console.log('✅ مكتبة uuid موجودة');
  } else {
    console.log('⚠️ مكتبة uuid غير موجودة في package.json');
  }
} else {
  console.log('⚠️ ملف package.json غير موجود');
}

console.log('\n🎉 تم تثبيت نظام الجلسات بنجاح!');
console.log('\n📋 الخطوات التالية:');
console.log('1. تشغيل المشروع: npm run dev');
console.log('2. زيارة صفحة الاختبار: http://localhost:3000/ar/test-sessions');
console.log('3. فتح عدة نوافذ لاختبار الجلسات المتعددة');
console.log('4. قراءة الدليل: SESSION_SYSTEM_README.md');

console.log('\n✨ نظام الجلسات جاهز للاستخدام!');
