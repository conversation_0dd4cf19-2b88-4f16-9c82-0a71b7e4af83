import { NextRequest, NextResponse } from 'next/server';
import { getContactInfo, updateContactInfo } from '@/lib/mysql-database';

// GET - جلب معلومات الاتصال
export async function GET() {
  try {
    const contactInfo = await getContactInfo();
    
    return NextResponse.json({
      success: true,
      contactInfo
    });
  } catch (error) {
    console.error('Error fetching contact info:', error);
    return NextResponse.json({
      success: false,
      message: 'خطأ في جلب معلومات الاتصال',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// POST - تحديث معلومات الاتصال
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, Password, host, port } = body;

    // التحقق من صحة البيانات
    if (!email) {
      return NextResponse.json({
        success: false,
        message: 'يرجى إدخال الإيميل'
      }, { status: 400 });
    }

    // التحقق من صحة الإيميل
    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      return NextResponse.json({
        success: false,
        message: 'صيغة الإيميل غير صحيحة'
      }, { status: 400 });
    }

    // تحديث معلومات الاتصال
    const updatedContactInfo = await updateContactInfo(
      email?.trim() || undefined,
      Password?.trim() || undefined,
      host?.trim() || undefined,
      port || undefined
    );

    return NextResponse.json({
      success: true,
      message: 'تم تحديث معلومات الاتصال بنجاح',
      contactInfo: updatedContactInfo
    });

  } catch (error) {
    console.error('Error updating contact info:', error);
    return NextResponse.json({
      success: false,
      message: 'خطأ في تحديث معلومات الاتصال',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
