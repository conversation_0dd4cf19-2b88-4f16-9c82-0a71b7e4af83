import { NextRequest, NextResponse } from 'next/server';
import { getCategories, addCategory } from '@/lib/mysql-database';
import { v4 as uuidv4 } from 'uuid';

// GET - جلب جميع الفئات
export async function GET() {
  try {
    const categories = await getCategories();
    return NextResponse.json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('Categories GET API error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal Server Error'
    }, { status: 500 });
  }
}

// POST - إضافة فئة جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, nameAr, description, descriptionAr, image, isActive } = body;

    if (!name || !nameAr) {
      return NextResponse.json({
        success: false,
        message: 'Name and Arabic name are required',
        messageAr: 'الاسم والاسم بالعربية مطلوبان'
      }, { status: 400 });
    }

    const categoryData = {
      id: uuidv4(),
      name,
      name_ar: nameAr,
      description: description || null,
      description_ar: descriptionAr || null,
      image: image || null,
      is_active: isActive !== undefined ? isActive : true
    };

    const newCategory = await addCategory(categoryData);
    return NextResponse.json({
      success: true,
      data: newCategory,
      message: 'Category created successfully',
      messageAr: 'تم إنشاء الفئة بنجاح'
    }, { status: 201 });

  } catch (error) {
    console.error('Categories POST API error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}
