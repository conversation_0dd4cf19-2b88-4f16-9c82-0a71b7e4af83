// دوال تحويل البيانات بين النظام القديم (JSON) والجديد (MySQL)

import { Category as OldCategory, Subcategory as OldSubcategory, Product as OldProduct } from '../types/database';
import { Category as NewCategory, Subcategory as NewSubcategory, Product as NewProduct, CategoryInput, SubcategoryInput, ProductInput } from '../types/mysql-database';

// تحويل فئة من النظام القديم إلى الجديد
export function convertCategoryToMySQL(oldCategory: OldCategory): CategoryInput {
  return {
    id: oldCategory.id,
    name: oldCategory.name,
    name_ar: oldCategory.nameAr,
    description: oldCategory.description || null,
    description_ar: oldCategory.descriptionAr || null,
    image_url: oldCategory.image || null,
    is_active: oldCategory.isActive
  };
}

// تحويل فئة من النظام الجديد إلى القديم
export function convertCategoryFromMySQL(newCategory: NewCategory): OldCategory {
  return {
    id: newCategory.id,
    name: newCategory.name,
    nameAr: newCategory.name_ar,
    description: newCategory.description || '',
    descriptionAr: newCategory.description_ar || '',
    image: newCategory.image_url || '',
    isActive: newCategory.is_active,
    createdAt: newCategory.created_at.toISOString(),
    updatedAt: newCategory.updated_at.toISOString()
  };
}

// تحويل فئة فرعية من النظام القديم إلى الجديد
export function convertSubcategoryToMySQL(oldSubcategory: OldSubcategory): SubcategoryInput {
  return {
    id: oldSubcategory.id,
    name: oldSubcategory.name,
    name_ar: oldSubcategory.nameAr,
    category_id: oldSubcategory.categoryId,
    description: oldSubcategory.description || null,
    description_ar: oldSubcategory.descriptionAr || null,
    is_active: oldSubcategory.isActive
  };
}

// تحويل فئة فرعية من النظام الجديد إلى القديم
export function convertSubcategoryFromMySQL(newSubcategory: NewSubcategory): OldSubcategory {
  return {
    id: newSubcategory.id,
    name: newSubcategory.name,
    nameAr: newSubcategory.name_ar,
    categoryId: newSubcategory.category_id,
    description: newSubcategory.description || '',
    descriptionAr: newSubcategory.description_ar || '',
    isActive: newSubcategory.is_active,
    createdAt: newSubcategory.created_at.toISOString(),
    updatedAt: newSubcategory.updated_at.toISOString()
  };
}

// تحويل منتج من النظام القديم إلى الجديد
export function convertProductToMySQL(oldProduct: OldProduct): {
  product: ProductInput;
  images: string[];
  features: string[];
  featuresAr: string[];
  specifications: Array<{
    nameEn: string;
    nameAr: string;
    valueEn: string;
    valueAr: string;
  }>;
} {
  return {
    product: {
      id: oldProduct.id,
      title: oldProduct.title,
      title_ar: oldProduct.titleAr,
      description: oldProduct.description || null,
      description_ar: oldProduct.descriptionAr || null,
      price: oldProduct.price,
      original_price: oldProduct.originalPrice || null,
      is_available: oldProduct.available,
      category_id: oldProduct.categoryId,
      subcategory_id: oldProduct.subcategoryId,
      is_active: oldProduct.isActive,
      is_featured: oldProduct.isFeatured
    },
    images: oldProduct.images || [],
    features: oldProduct.features || [],
    featuresAr: oldProduct.featuresAr || [],
    specifications: oldProduct.specifications || []
  };
}

// تحويل منتج من النظام الجديد إلى القديم
export function convertProductFromMySQL(
  newProduct: NewProduct,
  images: string[] = [],
  features: string[] = [],
  featuresAr: string[] = [],
  specifications: Array<{
    nameEn: string;
    nameAr: string;
    valueEn: string;
    valueAr: string;
  }> = []
): OldProduct {
  return {
    id: newProduct.id,
    title: newProduct.title,
    titleAr: newProduct.title_ar,
    description: newProduct.description || '',
    descriptionAr: newProduct.description_ar || '',
    images: images,
    price: newProduct.price,
    originalPrice: newProduct.original_price || null,
    available: newProduct.is_available,
    categoryId: newProduct.category_id,
    subcategoryId: newProduct.subcategory_id,
    features: features,
    featuresAr: featuresAr,
    specifications: specifications,
    isActive: newProduct.is_active,
    isFeatured: newProduct.is_featured,
    createdAt: newProduct.created_at.toISOString(),
    updatedAt: newProduct.updated_at.toISOString()
  };
}

// دالة لإنشاء ID من النص (مثل النظام القديم)
export function generateIdFromName(name: string): string {
  return name.toLowerCase()
    .replace(/[^a-z0-9]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '');
}

// دالة لإنشاء ID رقمي للمنتجات
export function generateNumericId(): string {
  return Date.now().toString();
}

// دالة لتحويل التاريخ من string إلى Date
export function convertStringToDate(dateString: string): Date {
  return new Date(dateString);
}

// دالة لتحويل التاريخ من Date إلى string
export function convertDateToString(date: Date): string {
  return date.toISOString();
}
