import { NextRequest, NextResponse } from 'next/server';
import { readSettingsFromFile, writeSettingsToFile, checkFileWritePermissions } from '@/lib/settings-file';
import { getSiteSettings } from '@/data/settings';

export async function POST(request: NextRequest) {

  try {
    console.log('🧪 بدء اختبار نظام الإعدادات...');

    const results = {
      filePermissions: false,
      fileRead: false,
      fileWrite: false,
      localStorageRead: false,
      timestamp: new Date().toISOString()
    };

    // اختبار صلاحيات الكتابة
    console.log('1️⃣ اختبار صلاحيات الكتابة...');
    results.filePermissions = checkFileWritePermissions();
    console.log(`   ${results.filePermissions ? '✅' : '❌'} صلاحيات الكتابة`);

    // اختبار قراءة الملف
    console.log('2️⃣ اختبار قراءة الملف...');
    try {
      const settingsFromFile = readSettingsFromFile();
      results.fileRead = true;
      console.log('   ✅ قراءة الملف نجحت');
      console.log('   📋 اسم الموقع من الملف:', settingsFromFile.siteName);
    } catch (error) {
      console.log('   ❌ فشل قراءة الملف:', error.message);
    }

    // اختبار كتابة الملف
    console.log('3️⃣ اختبار كتابة الملف...');
    try {
      const testSettings = {
        ...readSettingsFromFile(),
        siteName: 'VidMeet Test',
        siteNameAr: 'فيد ميت تجريبي',
        testField: 'اختبار الحفظ',
        lastTestUpdate: new Date().toISOString()
      };

      results.fileWrite = writeSettingsToFile(testSettings);
      console.log(`   ${results.fileWrite ? '✅' : '❌'} كتابة الملف`);

      if (results.fileWrite) {
        // التحقق من الكتابة بقراءة الملف مرة أخرى
        const verifySettings = readSettingsFromFile();
        if (verifySettings.testField === 'اختبار الحفظ') {
          console.log('   ✅ تم التحقق من الكتابة بنجاح');
        } else {
          console.log('   ⚠️ الكتابة تمت لكن البيانات لم تُحفظ بشكل صحيح');
          results.fileWrite = false;
        }
      }
    } catch (error) {
      console.log('   ❌ فشل كتابة الملف:', error.message);
    }

    // اختبار قراءة localStorage (محاكاة)
    console.log('4️⃣ اختبار قراءة localStorage...');
    try {
      const settingsFromLS = getSiteSettings();
      results.localStorageRead = true;
      console.log('   ✅ قراءة localStorage نجحت');
      console.log('   📋 اسم الموقع من localStorage:', settingsFromLS.siteName);
    } catch (error) {
      console.log('   ❌ فشل قراءة localStorage:', error.message);
    }

    console.log('🏁 انتهاء الاختبار');

  return NextResponse.json({
    success: true,
    message: 'Settings test completed',
    results,
    recommendations: generateRecommendations(results)
  });

  } catch (error) {
    console.error('❌ خطأ في اختبار الإعدادات:', error);
    return NextResponse.json({
      success: false,
      message: 'Test failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

function generateRecommendations(results: any): string[] {
  const recommendations = [];

  if (!results.filePermissions) {
    recommendations.push('تحقق من صلاحيات الكتابة في مجلد src/data/');
  }

  if (!results.fileRead) {
    recommendations.push('تأكد من وجود ملف src/data/settings.json');
  }

  if (!results.fileWrite) {
    recommendations.push('تحقق من إمكانية كتابة الملفات في النظام');
  }

  if (!results.localStorageRead) {
    recommendations.push('تحقق من دالة getSiteSettings في src/data/settings.ts');
  }

  if (results.filePermissions && results.fileRead && results.fileWrite && results.localStorageRead) {
    recommendations.push('جميع الاختبارات نجحت! النظام يعمل بشكل صحيح');
  }

  return recommendations;
}
