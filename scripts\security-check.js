const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// تحميل متغيرات البيئة من .env.local
function loadEnvFile() {
  const envPath = path.join(process.cwd(), '.env.local');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');

    lines.forEach(line => {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [key, ...valueParts] = trimmedLine.split('=');
        if (key && valueParts.length > 0) {
          const value = valueParts.join('=');
          process.env[key] = value;
        }
      }
    });
  }
}

// تحميل متغيرات البيئة عند بدء التشغيل
loadEnvFile();

// ألوان للطباعة
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

// دالة للطباعة الملونة
function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// فحص الأمان الشامل
class SecurityChecker {
  constructor() {
    this.issues = [];
    this.warnings = [];
    this.passed = [];
  }

  // إضافة مشكلة أمنية
  addIssue(severity, category, message, fix = null) {
    const issue = { severity, category, message, fix };
    if (severity === 'critical' || severity === 'high') {
      this.issues.push(issue);
    } else {
      this.warnings.push(issue);
    }
  }

  // إضافة فحص نجح
  addPassed(category, message) {
    this.passed.push({ category, message });
  }

  // فحص متغيرات البيئة
  checkEnvironmentVariables() {
    colorLog('blue', '\n🔍 فحص متغيرات البيئة...');
    
    const requiredVars = [
      'JWT_SECRET',
      'ENCRYPTION_KEY',
      'DEFAULT_ADMIN_PASSWORD'
    ];

    const weakSecrets = [
      'your-super-secret-jwt-key-change-this-in-production',
      'your-encryption-key-change-this-in-production',
      'SecureAdmin@2024!'
    ];

    // فحص وجود المتغيرات المطلوبة
    requiredVars.forEach(varName => {
      const value = process.env[varName];
      if (!value) {
        this.addIssue('critical', 'Environment', `متغير البيئة ${varName} غير موجود`, 
          `أضف ${varName} إلى ملف .env`);
      } else if (weakSecrets.includes(value)) {
        this.addIssue('critical', 'Environment', `متغير البيئة ${varName} يستخدم قيمة افتراضية ضعيفة`,
          `غيّر قيمة ${varName} إلى قيمة قوية وفريدة`);
      } else if (value.length < 32) {
        this.addIssue('high', 'Environment', `متغير البيئة ${varName} قصير جداً (أقل من 32 حرف)`,
          `استخدم قيمة أطول وأكثر تعقيداً لـ ${varName}`);
      } else {
        this.addPassed('Environment', `متغير البيئة ${varName} آمن`);
      }
    });
  }

  // فحص الملفات الحساسة
  checkSensitiveFiles() {
    colorLog('blue', '\n🔍 فحص الملفات الحساسة...');
    
    const sensitiveFiles = [
      'src/data/database.json',
      '.env',
      '.env.local',
      '.env.production'
    ];

    const shouldNotExist = [
      'src/data/database.json' // يجب أن يكون مشفراً الآن
    ];

    sensitiveFiles.forEach(filePath => {
      if (fs.existsSync(filePath)) {
        if (shouldNotExist.includes(filePath)) {
          this.addIssue('high', 'Files', `الملف ${filePath} لا يجب أن يكون موجوداً`,
            `احذف ${filePath} واستخدم النظام المشفر`);
        } else {
          // فحص محتوى الملف للكلمات المرور الضعيفة فقط
          try {
            const content = fs.readFileSync(filePath, 'utf8');
            // فحص كلمات المرور الضعيفة المحددة فقط
            const criticalWeakPasswords = ['admin123', 'password123', '123456', 'password', 'qwerty'];
            const hasCriticalWeakPassword = criticalWeakPasswords.some(weak => {
              // فحص دقيق للكلمات الضعيفة
              const regex = new RegExp(`(^|=|:)\\s*${weak}\\s*($|\\n)`, 'i');
              return regex.test(content);
            });

            if (hasCriticalWeakPassword) {
              this.addIssue('critical', 'Files', `الملف ${filePath} يحتوي على كلمات مرور ضعيفة معروفة`,
                `أزل كلمات المرور الضعيفة من ${filePath}`);
            } else if (filePath.includes('.env') && process.env.NODE_ENV === 'production') {
              // في الإنتاج، تحذير من وجود كلمات مرور في ملفات .env
              if (content.includes('PASSWORD=') || content.includes('SECRET=')) {
                this.addIssue('medium', 'Files', `الملف ${filePath} يحتوي على كلمات مرور في الإنتاج`,
                  `استخدم متغيرات البيئة الآمنة في الإنتاج`);
              }
            }
          } catch (error) {
            // تجاهل أخطاء القراءة
          }
        }
      }
    });

    // فحص وجود الملفات المشفرة
    const encryptedFiles = [
      'data/encrypted-database.enc',
      'data/secure-admin.enc'
    ];

    encryptedFiles.forEach(filePath => {
      if (fs.existsSync(filePath)) {
        this.addPassed('Files', `الملف المشفر ${filePath} موجود`);
      } else {
        this.addIssue('high', 'Files', `الملف المشفر ${filePath} غير موجود`,
          `قم بتشغيل scripts الترحيل لإنشاء الملفات المشفرة`);
      }
    });
  }

  // فحص إعدادات Next.js
  checkNextConfig() {
    colorLog('blue', '\n🔍 فحص إعدادات Next.js...');
    
    const configPath = 'next.config.js';
    if (!fs.existsSync(configPath)) {
      this.addIssue('medium', 'Config', 'ملف next.config.js غير موجود');
      return;
    }

    const content = fs.readFileSync(configPath, 'utf8');
    
    // فحص الإعدادات غير الآمنة
    if (content.includes('ignoreDuringBuilds: true')) {
      this.addIssue('medium', 'Config', 'ESLint معطل في البناء',
        'فعّل ESLint في الإنتاج');
    }

    if (content.includes('ignoreBuildErrors: true')) {
      this.addIssue('medium', 'Config', 'TypeScript errors معطلة في البناء',
        'فعّل فحص TypeScript في الإنتاج');
    }

    if (content.includes('poweredByHeader: false')) {
      this.addPassed('Config', 'X-Powered-By header مخفي');
    } else {
      this.addIssue('low', 'Config', 'X-Powered-By header مكشوف',
        'أضف poweredByHeader: false إلى next.config.js');
    }
  }

  // فحص الحماية من الهجمات الشائعة
  checkCommonVulnerabilities() {
    colorLog('blue', '\n🔍 فحص الحماية من الهجمات الشائعة...');
    
    // فحص وجود middleware الأمان
    if (fs.existsSync('middleware.ts')) {
      const content = fs.readFileSync('middleware.ts', 'utf8');
      
      if (content.includes('X-Frame-Options')) {
        this.addPassed('Security', 'حماية من Clickjacking موجودة');
      } else {
        this.addIssue('medium', 'Security', 'حماية من Clickjacking غير موجودة',
          'أضف X-Frame-Options header');
      }

      if (content.includes('Content-Security-Policy')) {
        this.addPassed('Security', 'Content Security Policy موجودة');
      } else {
        this.addIssue('medium', 'Security', 'Content Security Policy غير موجودة',
          'أضف CSP headers');
      }
    } else {
      this.addIssue('high', 'Security', 'ملف middleware.ts غير موجود',
        'أنشئ middleware للأمان');
    }

    // فحص وجود نظام Rate Limiting
    if (fs.existsSync('src/lib/rate-limiter.ts')) {
      this.addPassed('Security', 'نظام Rate Limiting موجود');
    } else {
      this.addIssue('high', 'Security', 'نظام Rate Limiting غير موجود',
        'أضف حماية من الهجمات المتكررة');
    }
  }

  // فحص قوة كلمات المرور
  checkPasswordStrength() {
    colorLog('blue', '\n🔍 فحص قوة كلمات المرور...');
    
    const password = process.env.DEFAULT_ADMIN_PASSWORD;
    if (!password) return;

    const checks = [
      { test: password.length >= 12, message: 'طول كلمة المرور مناسب (12+ حرف)' },
      { test: /[A-Z]/.test(password), message: 'تحتوي على أحرف كبيرة' },
      { test: /[a-z]/.test(password), message: 'تحتوي على أحرف صغيرة' },
      { test: /[0-9]/.test(password), message: 'تحتوي على أرقام' },
      { test: /[!@#$%^&*(),.?":{}|<>]/.test(password), message: 'تحتوي على رموز خاصة' },
      { test: !/(123|abc|password|admin|secret)$/i.test(password), message: 'لا تحتوي على كلمات شائعة' }
    ];

    checks.forEach(check => {
      if (check.test) {
        this.addPassed('Password', check.message);
      } else {
        this.addIssue('medium', 'Password', `كلمة المرور: ${check.message}`,
          'استخدم كلمة مرور أقوى');
      }
    });
  }

  // فحص أذونات الملفات
  checkFilePermissions() {
    colorLog('blue', '\n🔍 فحص أذونات الملفات...');
    
    const sensitiveFiles = [
      '.env',
      '.env.local',
      '.env.production',
      'data/encrypted-database.enc',
      'data/secure-admin.enc'
    ];

    sensitiveFiles.forEach(filePath => {
      if (fs.existsSync(filePath)) {
        try {
          const stats = fs.statSync(filePath);
          const mode = stats.mode & parseInt('777', 8);
          
          if (mode > parseInt('600', 8)) {
            this.addIssue('medium', 'Permissions', `الملف ${filePath} له أذونات واسعة`,
              `غيّر أذونات ${filePath} إلى 600`);
          } else {
            this.addPassed('Permissions', `أذونات الملف ${filePath} آمنة`);
          }
        } catch (error) {
          // تجاهل أخطاء فحص الأذونات في Windows
        }
      }
    });
  }

  // تشغيل جميع الفحوصات
  runAllChecks() {
    colorLog('bold', '🔒 بدء فحص الأمان الشامل...\n');
    
    this.checkEnvironmentVariables();
    this.checkSensitiveFiles();
    this.checkNextConfig();
    this.checkCommonVulnerabilities();
    this.checkPasswordStrength();
    this.checkFilePermissions();
    
    this.generateReport();
  }

  // إنشاء التقرير النهائي
  generateReport() {
    colorLog('bold', '\n📊 تقرير الأمان النهائي');
    colorLog('bold', '='.repeat(50));
    
    // عرض النتائج الإيجابية
    if (this.passed.length > 0) {
      colorLog('green', `\n✅ الفحوصات الناجحة (${this.passed.length}):`);
      this.passed.forEach(item => {
        colorLog('green', `  ✓ ${item.category}: ${item.message}`);
      });
    }

    // عرض التحذيرات
    if (this.warnings.length > 0) {
      colorLog('yellow', `\n⚠️  التحذيرات (${this.warnings.length}):`);
      this.warnings.forEach(item => {
        colorLog('yellow', `  ⚠ ${item.category}: ${item.message}`);
        if (item.fix) {
          colorLog('yellow', `    💡 الحل: ${item.fix}`);
        }
      });
    }

    // عرض المشاكل الحرجة
    if (this.issues.length > 0) {
      colorLog('red', `\n❌ المشاكل الأمنية (${this.issues.length}):`);
      this.issues.forEach(item => {
        const icon = item.severity === 'critical' ? '🚨' : '⚠️';
        colorLog('red', `  ${icon} ${item.category}: ${item.message}`);
        if (item.fix) {
          colorLog('red', `    💡 الحل: ${item.fix}`);
        }
      });
    }

    // النتيجة النهائية
    colorLog('bold', '\n' + '='.repeat(50));
    
    if (this.issues.length === 0) {
      colorLog('green', '🎉 تهانينا! المشروع آمن وجاهز للنشر');
      process.exit(0);
    } else {
      colorLog('red', `❌ يجب إصلاح ${this.issues.length} مشكلة أمنية قبل النشر`);
      process.exit(1);
    }
  }
}

// تشغيل فحص الأمان
if (require.main === module) {
  const checker = new SecurityChecker();
  checker.runAllChecks();
}

module.exports = SecurityChecker;
