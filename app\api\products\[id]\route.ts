import { NextRequest, NextResponse } from 'next/server';
import { getProductWithDetails } from '@/lib/mysql-database';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    if (!id || typeof id !== 'string') {
      return NextResponse.json({
        success: false,
        error: 'Invalid product ID',
        messageAr: 'معرف المنتج غير صحيح'
      }, { status: 400 });
    }

    const product = await getProductWithDetails(id);
    if (!product) {
      return NextResponse.json({
        success: false,
        error: 'Product not found',
        messageAr: 'المنتج غير موجود'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: product
    });
  } catch (error) {
    console.error('Product API Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}
