import { NextApiRequest, NextApiResponse } from 'next';
import formidable from 'formidable';
import fs from 'fs';
import path from 'path';
import { requireAdminAuth } from '../../../lib/auth';
import { saveFileSecurely } from '../../../lib/secure-upload';
import { checkRateLimit, RATE_LIMIT_CONFIGS, getClientIP } from '../../../lib/rate-limiter';

export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // TODO: إعادة تفعيل المصادقة لاحقاً
  // const user = requireAdminAuth(req);
  // if (!user) {
  //   return res.status(401).json({
  //     success: false,
  //     message: 'Authentication required',
  //     messageAr: 'المصادقة مطلوبة'
  //   });
  // }

  // فحص معدل الطلبات لرفع الملفات
  const clientIP = getClientIP(req);
  const rateLimitResult = checkRateLimit(req, 'upload', RATE_LIMIT_CONFIGS.UPLOAD);

  if (!rateLimitResult.allowed) {
    return res.status(429).json({
      success: false,
      message: 'Too many upload requests. Please try again later.',
      messageAr: 'طلبات رفع كثيرة جداً. يرجى المحاولة لاحقاً.',
      retryAfter: rateLimitResult.retryAfter
    });
  }

  try {
    // إنشاء مجلد مؤقت للرفع
    const tempDir = path.join(process.cwd(), 'temp-uploads');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    const form = formidable({
      uploadDir: tempDir,
      keepExtensions: true,
      maxFileSize: 5 * 1024 * 1024, // 5MB (تم تقليل الحد الأقصى)
      filter: (part) => {
        // فحص أولي للنوع
        return !!(part.mimetype && part.mimetype.startsWith('image/'));
      },
    });

    const [fields, files] = await form.parse(req);

    const uploadedFiles: string[] = [];
    const errors: string[] = [];

    // معالجة الملفات المرفوعة بشكل آمن
    for (const [fieldName, fileArray] of Object.entries(files)) {
      const filesToProcess = Array.isArray(fileArray) ? fileArray : [fileArray];

      for (const file of filesToProcess) {
        if (file && file.filepath && file.originalFilename && file.mimetype) {
          try {
            // حفظ الملف بشكل آمن
            const secureFilename = saveFileSecurely(
              file.filepath,
              file.originalFilename,
              file.mimetype
            );

            if (secureFilename) {
              // إضافة رابط الملف الآمن
              uploadedFiles.push(`/api/files/${secureFilename}`);
            } else {
              errors.push(`فشل في حفظ الملف: ${file.originalFilename}`);
            }
          } catch (error) {
            console.error('Error processing file:', error);
            errors.push(`خطأ في معالجة الملف: ${file.originalFilename}`);
          }
        }
      }
    }

    // إرجاع النتائج
    if (uploadedFiles.length === 0 && errors.length > 0) {
      return res.status(400).json({
        success: false,
        error: 'No valid files uploaded',
        errors: errors,
        message: 'فشل في رفع جميع الملفات'
      });
    }

    res.status(200).json({
      success: true,
      files: uploadedFiles,
      errors: errors.length > 0 ? errors : undefined,
      message: `تم رفع ${uploadedFiles.length} ملف بنجاح${errors.length > 0 ? ` مع ${errors.length} أخطاء` : ''}`
    });

  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ error: 'Failed to upload files' });
  }
}
