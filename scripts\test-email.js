// سكريبت اختبار إعدادات البريد الإلكتروني مع Hostinger
const nodemailer = require('nodemailer');
require('dotenv').config({ path: '.env.local' });

async function testEmailSettings() {
  console.log('🔄 بدء اختبار إعدادات البريد الإلكتروني...\n');

  // قراءة إعدادات البيئة
  const emailUser = process.env.EMAIL_USER;
  const emailPass = process.env.EMAIL_PASS;
  const adminEmail = process.env.ADMIN_EMAIL;
  const smtpHost = process.env.SMTP_HOST || 'smtp.hostinger.com';
  const smtpPort = parseInt(process.env.SMTP_PORT || '465');

  // التحقق من وجود الإعدادات
  console.log('📋 التحقق من إعدادات البيئة:');
  console.log(`📧 EMAIL_USER: ${emailUser ? '✅ موجود' : '❌ مفقود'}`);
  console.log(`🔑 EMAIL_PASS: ${emailPass ? '✅ موجود' : '❌ مفقود'}`);
  console.log(`📬 ADMIN_EMAIL: ${adminEmail ? '✅ موجود' : '❌ مفقود'}`);
  console.log(`🏠 SMTP_HOST: ${smtpHost}`);
  console.log(`🔌 SMTP_PORT: ${smtpPort}\n`);

  if (!emailUser || !emailPass || !adminEmail) {
    console.error('❌ إعدادات البريد الإلكتروني غير مكتملة في ملف .env.local');
    console.log('\n📝 يرجى التأكد من إضافة المتغيرات التالية:');
    console.log('EMAIL_USER=<EMAIL>');
    console.log('EMAIL_PASS=your-email-password');
    console.log('ADMIN_EMAIL=<EMAIL>');
    console.log('SMTP_HOST=smtp.hostinger.com');
    console.log('SMTP_PORT=465');
    return;
  }

  try {
    console.log('🔄 إنشاء اتصال SMTP...');
    
    // إنشاء transporter
    const transporter = nodemailer.createTransport({
      host: smtpHost,
      port: smtpPort,
      secure: smtpPort === 465, // true for 465, false for other ports
      auth: {
        user: emailUser,
        pass: emailPass
      },
      tls: {
        rejectUnauthorized: false
      }
    });

    console.log('🔍 التحقق من صحة الإعدادات...');
    
    // اختبار الاتصال
    await transporter.verify();
    console.log('✅ تم التحقق من إعدادات SMTP بنجاح!\n');

    console.log('📤 إرسال إيميل تجريبي...');
    
    // إرسال إيميل تجريبي
    const testEmailContent = `
      <div dir="rtl" style="font-family: Arial, sans-serif; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
          <h2 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">
            🧪 اختبار إعدادات البريد الإلكتروني
          </h2>
          
          <p>مرحباً!</p>
          
          <p>هذا إيميل تجريبي للتأكد من أن إعدادات البريد الإلكتروني تعمل بشكل صحيح.</p>
          
          <div style="background-color: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3 style="color: #27ae60; margin: 0;">✅ الإعدادات المستخدمة:</h3>
            <ul style="margin: 10px 0;">
              <li><strong>الخادم:</strong> ${smtpHost}</li>
              <li><strong>المنفذ:</strong> ${smtpPort}</li>
              <li><strong>المرسل:</strong> ${emailUser}</li>
              <li><strong>المستقبل:</strong> ${adminEmail}</li>
            </ul>
          </div>
          
          <p>إذا وصلك هذا الإيميل، فهذا يعني أن الإعدادات صحيحة ويمكن للنظام إرسال طلبات التسعير بنجاح.</p>
          
          <p style="color: #7f8c8d; font-style: italic;">
            تم إرسال هذا الإيميل في: ${new Date().toLocaleString('ar-SA')}
          </p>
        </div>
      </div>
    `;

    const mailOptions = {
      from: `"DROOB HAJER - اختبار" <${emailUser}>`,
      to: adminEmail,
      subject: `🧪 اختبار إعدادات البريد الإلكتروني - ${new Date().toLocaleDateString('ar-SA')}`,
      html: testEmailContent
    };

    const info = await transporter.sendMail(mailOptions);
    
    console.log('✅ تم إرسال الإيميل التجريبي بنجاح!');
    console.log(`🆔 Message ID: ${info.messageId}`);
    console.log(`📊 استجابة الخادم: ${info.response}\n`);
    
    console.log('🎉 جميع الاختبارات نجحت!');
    console.log('📧 تحقق من صندوق الوارد في:', adminEmail);
    console.log('📝 إذا لم تجد الإيميل، تحقق من مجلد الرسائل المزعجة (Spam)');

  } catch (error) {
    console.error('\n❌ فشل في اختبار إعدادات البريد الإلكتروني:');
    console.error('📄 تفاصيل الخطأ:', error.message);
    
    if (error.code === 'EAUTH') {
      console.log('\n💡 نصائح لحل مشكلة المصادقة:');
      console.log('1. تأكد من صحة EMAIL_USER و EMAIL_PASS');
      console.log('2. تأكد من أن الإيميل مُفعَّل في Hostinger');
      console.log('3. تأكد من أن كلمة المرور صحيحة');
    } else if (error.code === 'ECONNECTION' || error.code === 'ETIMEDOUT') {
      console.log('\n💡 نصائح لحل مشكلة الاتصال:');
      console.log('1. تأكد من SMTP_HOST = smtp.hostinger.com');
      console.log('2. جرب المنفذ 587 بدلاً من 465');
      console.log('3. تأكد من اتصال الإنترنت');
    } else {
      console.log('\n💡 نصائح عامة:');
      console.log('1. تحقق من جميع إعدادات البيئة');
      console.log('2. تواصل مع دعم Hostinger الفني');
      console.log('3. تأكد من أن خدمة البريد الإلكتروني مُفعَّلة');
    }
  }
}

// تشغيل الاختبار
testEmailSettings().catch(console.error);
