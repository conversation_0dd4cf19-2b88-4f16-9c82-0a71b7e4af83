'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Locale } from '../lib/i18n';
import { ProductWithDetails } from '../types/mysql-database';
import ProductCard from './ProductCard';

interface FeaturedProductsProps {
  locale: Locale;
}

const FeaturedProducts: React.FC<FeaturedProductsProps> = ({ locale }) => {
  const [products, setProducts] = useState<ProductWithDetails[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchFeaturedProducts = async () => {
      try {
        const response = await fetch('/api/products?featured=true');
        if (response.ok) {
          const result = await response.json();
          console.log('📦 استجابة API المنتجات المميزة:', result);

          if (result.success && result.data) {
            // أخذ أول 6 منتجات مميزة
            setProducts(result.data.slice(0, 6));
          } else {
            console.error('❌ فشل في جلب المنتجات المميزة:', result);
            setProducts([]);
          }
        } else {
          console.error('❌ خطأ في استجابة المنتجات المميزة:', response.status);
          setProducts([]);
        }
      } catch (error) {
        console.error('Error fetching featured products:', error);
        setProducts([]);
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedProducts();
  }, []);

  if (loading) {
    return (
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600">
              {locale === 'ar' ? 'جاري تحميل المنتجات المميزة...' : 'Loading featured products...'}
            </p>
          </div>
        </div>
      </section>
    );
  }

  if (products.length === 0) {
    return null;
  }

  return (
    <section className="py-16">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            {locale === 'ar' ? 'المنتجات المميزة' : 'Featured Products'}
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            {locale === 'ar' 
              ? 'اكتشف أفضل منتجاتنا المختارة بعناية لتلبية احتياجاتك'
              : 'Discover our best products carefully selected to meet your needs'
            }
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {products.map((product) => (
            <ProductCard
              key={product.id}
              id={product.id}
              image={product.images?.[0]?.image_url || '/api/placeholder?width=400&height=300&text=لا توجد صورة'}
              title={locale === 'ar' ? product.title_ar : product.title}
              description={locale === 'ar' ? (product.description_ar || '') : (product.description || '')}
              price={product.price}
              available={product.is_available}
              locale={locale}
            />
          ))}
        </div>

        <div className="text-center">
          <Link
            href={`/${locale}/products`}
            className="inline-flex items-center gap-2 bg-primary hover:bg-primary/90 text-white px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 transform hover:scale-105 hover:shadow-xl"
          >
            {locale === 'ar' ? 'عرض جميع المنتجات' : 'View All Products'}
            <i className="ri-arrow-right-line"></i>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default FeaturedProducts;
