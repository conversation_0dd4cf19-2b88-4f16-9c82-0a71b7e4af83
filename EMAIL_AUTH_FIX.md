# حل مشكلة المصادقة مع Hostinger SMTP

## 🚨 المشكلة الحالية
```
❌ خطأ في إرسال الإيميل عبر Hostinger SMTP:
Error: Invalid login: 535 5.7.8 Error: authentication failed
```

## 🔧 الحلول المطلوبة

### 1. التحقق من إعدادات Hostinger

#### أ) تسجيل الدخول إلى لوحة تحكم Hostinger:
1. اذهب إلى [hpanel.hostinger.com](https://hpanel.hostinger.com)
2. اختر النطاق الخاص بك
3. اذهب إلى قسم "Email"

#### ب) التحقق من حساب البريد الإلكتروني:
1. تأكد من أن الإيميل `<EMAIL>` موجود ومُفعَّل
2. تأكد من كلمة المرور الصحيحة
3. تحقق من عدم انتهاء صلاحية الحساب

### 2. إعدادات SMTP الصحيحة لـ Hostinger

#### الإعدادات المطلوبة:
```
SMTP Server: smtp.hostinger.com
Port: 465 (SSL) أو 587 (TLS)
Security: SSL/TLS
Authentication: Required
```

#### في ملف `.env.local`:
```env
EMAIL_USER=<EMAIL>
EMAIL_PASS=كلمة_المرور_الصحيحة
SMTP_HOST=smtp.hostinger.com
SMTP_PORT=465
```

### 3. مشاكل شائعة وحلولها

#### أ) كلمة مرور خاطئة:
- **المشكلة:** كلمة المرور في `.env.local` غير صحيحة
- **الحل:** تحديث كلمة المرور من لوحة تحكم Hostinger

#### ب) الإيميل غير مُفعَّل:
- **المشكلة:** حساب البريد الإلكتروني معطل أو محذوف
- **الحل:** إعادة تفعيل الحساب من لوحة التحكم

#### ج) مشكلة في الخادم:
- **المشكلة:** خادم SMTP غير صحيح
- **الحل:** استخدام `smtp.hostinger.com` بدلاً من خوادم أخرى

#### د) مشكلة في المنفذ:
- **المشكلة:** المنفذ 465 محجوب
- **الحل:** جرب المنفذ 587 مع TLS

### 4. خطوات التشخيص

#### أ) اختبار الإعدادات:
```bash
npm run test-email
```

#### ب) فحص متغيرات البيئة:
```bash
# تأكد من وجود هذه المتغيرات في .env.local
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_actual_password
SMTP_HOST=smtp.hostinger.com
SMTP_PORT=465
```

#### ج) اختبار الاتصال يدوياً:
يمكنك استخدام أي برنامج إيميل (مثل Outlook) لاختبار الإعدادات:
- Server: smtp.hostinger.com
- Port: 465
- Security: SSL
- Username: <EMAIL>
- Password: كلمة المرور الفعلية

### 5. إعدادات بديلة

إذا لم تعمل الإعدادات الحالية، جرب:

#### الإعداد البديل 1 (TLS):
```env
SMTP_HOST=smtp.hostinger.com
SMTP_PORT=587
```

#### الإعداد البديل 2 (خادم بديل):
```env
SMTP_HOST=mail.droobhajer.com
SMTP_PORT=465
```

### 6. التحقق من حالة الخدمة

#### أ) حالة خوادم Hostinger:
- تحقق من [status.hostinger.com](https://status.hostinger.com)
- تأكد من عدم وجود مشاكل في خدمة البريد الإلكتروني

#### ب) اختبار DNS:
```bash
nslookup smtp.hostinger.com
```

### 7. الحصول على المساعدة

#### معلومات مطلوبة عند التواصل مع دعم Hostinger:
1. النطاق: droobhajer.com
2. الإيميل المتأثر: <EMAIL>
3. رسالة الخطأ: "535 5.7.8 Error: authentication failed"
4. الإعدادات المستخدمة: smtp.hostinger.com:465

#### رقم دعم Hostinger:
- الدعم الفني متاح 24/7 عبر الدردشة المباشرة في لوحة التحكم

## ✅ قائمة التحقق السريع

- [ ] تحقق من وجود الإيميل في لوحة تحكم Hostinger
- [ ] تأكد من كلمة المرور الصحيحة
- [ ] تحديث `.env.local` بالإعدادات الصحيحة
- [ ] اختبار الإعدادات باستخدام `npm run test-email`
- [ ] جرب المنفذ 587 إذا لم يعمل 465
- [ ] تواصل مع دعم Hostinger إذا استمرت المشكلة

---

**ملاحظة مهمة:** تأكد من أن النطاق `droobhajer.com` مُعرَّف بشكل صحيح في DNS وأن خدمة البريد الإلكتروني مُفعَّلة في حساب Hostinger.
