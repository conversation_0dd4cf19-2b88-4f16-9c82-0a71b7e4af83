# دليل إعداد نظام التواصل التلقائي (الإيميل والواتساب)

## نظرة عامة

تم إضافة نظام التواصل الشامل الذي يشمل الإيميل والواتساب. عندما يقوم العميل بإرسال طلب تسعير، سيتم:

1. حفظ البيانات في ملف JSON
2. إنشاء ملف Excel للمنتجات المطلوبة
3. **إرسال نسخة من ملف Excel تلقائياً إلى الإيميل المحدد**
4. **إمكانية التواصل مع العملاء عبر الواتساب باستخدام الرقم المحدد**

## الميزات الجديدة

### 1. إرسال الإيميل التلقائي
- إرسال ملف Excel مع تفاصيل طلب التسعير
- إيميل منسق باللغة العربية مع تفاصيل العميل والمنتجات
- إمكانية تفعيل/إلغاء تفعيل الإرسال التلقائي

### 2. إعدادات الواتساب الديناميكية
- رقم واتساب موحد لجميع أزرار الواتساب في الموقع
- رسائل ترحيب قابلة للتخصيص (عربي/إنجليزي)
- رسائل خاصة لطلبات التسعير
- إمكانية تفعيل/إلغاء تفعيل الواتساب

### 3. لوحة تحكم إعدادات التواصل
- تبويب موحد "إعدادات التواصل" يشمل الإيميل والواتساب
- إعدادات SMTP قابلة للتخصيص
- دعم Gmail وخدمات الإيميل الأخرى
- إدارة شاملة لإعدادات الواتساب

## إعداد Gmail

### الخطوات المطلوبة:

1. **تفعيل المصادقة الثنائية**
   - انتقل إلى إعدادات حساب Google
   - فعل المصادقة الثنائية (2FA)

2. **إنشاء كلمة مرور التطبيق**
   - انتقل إلى: Google Account → Security → App passwords
   - اختر "Mail" كتطبيق
   - انسخ كلمة مرور التطبيق المُنشأة

3. **إعداد الإعدادات في لوحة التحكم**
   - انتقل إلى: لوحة التحكم → إعدادات الموقع → إعدادات التواصل
   - في قسم إعدادات الإيميل، املأ البيانات التالية:
     - خادم SMTP: `smtp.gmail.com`
     - منفذ SMTP: `587`
     - اسم المستخدم: إيميلك الكامل
     - كلمة مرور التطبيق: الكلمة المُنشأة في الخطوة 2
     - الإيميل المستقبل: الإيميل الذي سيستقبل طلبات التسعير
   - فعل خيار "تفعيل إرسال الإيميل التلقائي"

## إعداد الواتساب

### الخطوات المطلوبة:

1. **الحصول على رقم واتساب تجاري**
   - تأكد من أن لديك رقم واتساب مخصص للعمل
   - يُفضل استخدام WhatsApp Business

2. **إعداد الإعدادات في لوحة التحكم**
   - انتقل إلى: لوحة التحكم → إعدادات الموقع → إعدادات التواصل
   - في قسم إعدادات الواتساب، املأ البيانات التالية:
     - رقم الواتساب التجاري: `+966501234567` (يجب أن يتضمن رمز الدولة)
     - رسالة الترحيب بالعربية: الرسالة التي تظهر عند الضغط على أيقونة الواتساب
     - رسالة الترحيب بالإنجليزية: نفس الرسالة بالإنجليزية
     - رسالة الرد على طلب التسعير بالعربية: الرسالة المرسلة للعملاء من لوحة التحكم
     - رسالة الرد على طلب التسعير بالإنجليزية: نفس الرسالة بالإنجليزية
   - فعل خيار "تفعيل الواتساب"

### استخدامات الواتساب في الموقع:

1. **أيقونة الواتساب العائمة**: تستخدم رسالة الترحيب
2. **أزرار الواتساب في المنتجات**: تتضمن تفاصيل المنتج
3. **أزرار الواتساب في الصفحات**: تستخدم رسائل مخصصة حسب السياق
4. **التواصل من لوحة التحكم**: يستخدم رسالة الرد على طلب التسعير

## إعداد خدمات إيميل أخرى

### Outlook/Hotmail:
- خادم SMTP: `smtp-mail.outlook.com`
- منفذ: `587`
- SSL/TLS: مُفعل

### Yahoo:
- خادم SMTP: `smtp.mail.yahoo.com`
- منفذ: `587` أو `465`
- SSL/TLS: مُفعل

## متغيرات البيئة (اختيارية)

يمكنك أيضاً إعداد الإيميل عبر متغيرات البيئة في ملف `.env.local`:

```env
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
ADMIN_EMAIL=<EMAIL>
```

## كيفية عمل النظام

### عند إرسال طلب تسعير:

1. **حفظ البيانات**: يتم حفظ بيانات العميل والمنتجات في ملف JSON
2. **إنشاء Excel**: يتم إنشاء ملف Excel يحتوي على:
   - رقم المنتج
   - اسم المنتج
   - الكمية المطلوبة
   - حقل فارغ للسعر المقترح
   - حقل فارغ للملاحظات

3. **إرسال الإيميل**: يتم إرسال إيميل يحتوي على:
   - تفاصيل طلب التسعير
   - بيانات العميل
   - قائمة المنتجات المطلوبة
   - ملف Excel مرفق

### محتوى الإيميل:

```
طلب تسعير جديد - QR-[ID]

رقم الطلب: QR-[ID]
تاريخ الطلب: [التاريخ]

بيانات العميل:
- الاسم: [اسم العميل]
- البريد الإلكتروني: [إيميل العميل]
- رقم الهاتف: [رقم الهاتف]
- الشركة: [اسم الشركة]

المنتجات المطلوبة:
- [قائمة المنتجات مع الكميات]

إجمالي عدد المنتجات: [العدد]
إجمالي الكمية: [الكمية الإجمالية]

يرجى مراجعة ملف الإكسل المرفق لتفاصيل أكثر.
```

## استكشاف الأخطاء

### مشاكل شائعة:

1. **فشل إرسال الإيميل**
   - تأكد من صحة بيانات SMTP
   - تأكد من تفعيل المصادقة الثنائية
   - تأكد من استخدام كلمة مرور التطبيق وليس كلمة المرور العادية

2. **عدم وصول الإيميل**
   - تحقق من مجلد الرسائل غير المرغوب فيها
   - تأكد من صحة الإيميل المستقبل

3. **خطأ في الاتصال**
   - تأكد من إعدادات المنفذ والتشفير
   - للـ Gmail استخدم المنفذ 587 بدون SSL

## الملفات المُحدثة

### ملفات النظام الأساسية:
- `src/pages/api/quote-requests.ts` - إضافة وظيفة إرسال الإيميل
- `src/pages/admin/settings.tsx` - إضافة تبويب إعدادات التواصل (إيميل + واتساب)
- `src/types/admin.ts` - إضافة أنواع إعدادات التواصل
- `src/data/settings.ts` - إضافة الإعدادات الافتراضية للتواصل
- `.env.local` - إضافة متغيرات البيئة للإيميل

### ملفات مكونات الواتساب:
- `components/WhatsAppButton.tsx` - تحديث لاستخدام الرقم من الإعدادات
- `components/HeroSection.tsx` - تحديث رابط الواتساب
- `components/ProductCard.tsx` - تحديث رابط الواتساب
- `components/ServicesSection.tsx` - تحديث رابط الواتساب
- `app/[locale]/product/[id]/page.tsx` - تحديث رابط الواتساب
- `src/components/WhatsAppButton.tsx` - تحديث للنسخة القديمة
- `src/components/ProductCard.tsx` - تحديث للنسخة القديمة
- `src/components/CTASection.tsx` - تحديث رابط الواتساب

### ملفات لوحة التحكم:
- `src/pages/admin/dashboard.tsx` - تحديث رسائل الواتساب
- `src/pages/admin/quote-requests.tsx` - تحديث رسائل الواتساب
- `app/[locale]/dashboard/page.tsx` - تحديث رسائل الواتساب

## الدعم

في حالة مواجهة أي مشاكل، تأكد من:
- تفعيل إعدادات الإيميل في لوحة التحكم
- صحة بيانات SMTP
- وجود اتصال بالإنترنت
- عدم حظر المنافذ من قبل الخادم أو جدار الحماية
