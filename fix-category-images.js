const mysql = require('mysql2/promise');

async function fixCategoryImages() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root', 
      password: '',
      database: 'droobhajer_db'
    });
    
    console.log('🔧 إصلاح صور الفئات...');
    
    // تحديث الفئات بصور موجودة فعلياً
    await connection.execute(`
      UPDATE categories
      SET image_url = 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop'
      WHERE name = 'F&B'
    `);
    
    // إضافة المزيد من الفئات إذا لم تكن موجودة
    const [existingCategories] = await connection.execute('SELECT COUNT(*) as count FROM categories WHERE deleted_at IS NULL');
    
    if (existingCategories[0].count < 3) {
      console.log('📦 إضافة فئات إضافية...');
      
      await connection.execute(`
        INSERT IGNORE INTO categories (id, name, name_ar, description, description_ar, image_url, is_active)
        VALUES 
        ('cat-kitchen', 'Kitchen Equipment', 'معدات المطبخ', 'Professional kitchen equipment and appliances', 'معدات وأجهزة المطبخ المهنية', 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop', 1),
        ('cat-dining', 'Dining & Service', 'الطعام والخدمة', 'Dining room and service equipment', 'معدات غرفة الطعام والخدمة', 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop', 1),
        ('cat-cleaning', 'Cleaning Supplies', 'مستلزمات التنظيف', 'Professional cleaning equipment and supplies', 'معدات ومستلزمات التنظيف المهنية', 'https://images.unsplash.com/photo-1563453392212-326f5e854473?w=400&h=300&fit=crop', 1)
      `);
    }
    
    console.log('✅ تم إصلاح صور الفئات');
    
    // عرض النتائج
    const [categories] = await connection.execute('SELECT id, name, name_ar, image_url FROM categories WHERE deleted_at IS NULL');
    console.log('\n📋 الفئات المحدثة:');
    categories.forEach(cat => {
      console.log(`- ${cat.name_ar} (${cat.name})`);
      console.log(`  الصورة: ${cat.image_url}`);
      console.log('');
    });
    
    await connection.end();
  } catch (error) {
    console.error('❌ خطأ:', error.message);
  }
}

fixCategoryImages();
