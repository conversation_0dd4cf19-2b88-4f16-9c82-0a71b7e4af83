import { NextRequest, NextResponse } from 'next/server';
import {
  getProductsWithDetails,
  getFeaturedProducts,
  getProductsByCategoryWithDetails,
  getProductsBySubcategoryWithDetails,
  getProductWithDetails
} from '@/lib/mysql-database';
import { ProductWithDetails } from '@/types/mysql-database';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const featured = searchParams.get('featured');
    const categoryId = searchParams.get('categoryId');
    const subcategoryId = searchParams.get('subcategoryId');
    const id = searchParams.get('id');

    let products: ProductWithDetails[];

    // إذا تم تمرير ID محدد، جلب منتج واحد مع التفاصيل
    if (id && typeof id === 'string') {
      const product = await getProductWithDetails(id);
      if (!product) {
        return NextResponse.json({
          success: false,
          message: 'Product not found',
          messageAr: 'المنتج غير موجود'
        }, { status: 404 });
      }
      return NextResponse.json({ success: true, data: product });
    }

    // جلب المنتجات حسب الفلترة
    if (featured === 'true') {
      products = await getFeaturedProducts();
    } else if (categoryId && typeof categoryId === 'string') {
      products = await getProductsByCategoryWithDetails(categoryId);
    } else if (subcategoryId && typeof subcategoryId === 'string') {
      products = await getProductsBySubcategoryWithDetails(subcategoryId);
    } else {
      products = await getProductsWithDetails();
    }

    return NextResponse.json({
      success: true,
      data: products
    });
  } catch (error) {
    console.error('Products API Error:', error);
    return NextResponse.json({ 
      success: false,
      error: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}
