# نظام قاعدة البيانات JSON - لوحة التحكم

## 🗄️ نظرة عامة

تم إنشاء نظام قاعدة بيانات متكامل باستخدام ملف JSON لتخزين جميع البيانات مع إمكانية الإضافة والتعديل والحذف بشكل ديناميكي من لوحة التحكم.

## 📁 هيكل الملفات

### ملف قاعدة البيانات
- `src/data/database.json` - ملف JSON الرئيسي لتخزين البيانات

### ملفات النظام
- `src/utils/database.ts` - دوال التعامل مع قاعدة البيانات
- `src/utils/api.ts` - دوال API للواجهة الأمامية
- `src/pages/api/admin/` - API endpoints للخادم

## 🏗️ هيكل قاعدة البيانات

### الفئات الرئيسية (Categories)
```json
{
  "id": "string",
  "name": "string",
  "nameAr": "string", 
  "description": "string",
  "descriptionAr": "string",
  "image": "string",
  "isActive": "boolean",
  "createdAt": "Date",
  "updatedAt": "Date"
}
```

### الفئات الفرعية (Subcategories)
```json
{
  "id": "string",
  "name": "string",
  "nameAr": "string",
  "categoryId": "string",
  "description": "string", 
  "descriptionAr": "string",
  "image": "string",
  "isActive": "boolean",
  "createdAt": "Date",
  "updatedAt": "Date"
}
```

### المنتجات (Products)
```json
{
  "id": "string",
  "title": "string",
  "titleAr": "string",
  "description": "string",
  "descriptionAr": "string", 
  "images": ["string"],
  "price": "number",
  "originalPrice": "number",
  "available": "boolean",
  "categoryId": "string",
  "subcategoryId": "string",
  "features": ["string"],
  "featuresAr": ["string"],
  "specifications": "object",
  "specificationsAr": "object",
  "isActive": "boolean",
  "isFeatured": "boolean",
  "createdAt": "Date",
  "updatedAt": "Date"
}
```

### إعدادات الموقع (Settings)
```json
{
  "siteName": "string",
  "siteNameAr": "string",
  "contactEmail": "string",
  "whatsappNumber": "string",
  "socialLinks": "object",
  "heroImages": ["string"],
  "aboutText": "string",
  "aboutTextAr": "string",
  "address": "string",
  "addressAr": "string",
  "phone": "string",
  "workingHours": "string",
  "workingHoursAr": "string"
}
```

## 🔧 API Endpoints

### الفئات الرئيسية
- `GET /api/admin/categories` - جلب جميع الفئات
- `POST /api/admin/categories` - إضافة فئة جديدة
- `PUT /api/admin/categories?id={id}` - تحديث فئة
- `DELETE /api/admin/categories?id={id}` - حذف فئة

### الفئات الفرعية
- `GET /api/admin/subcategories` - جلب جميع الفئات الفرعية
- `GET /api/admin/subcategories?categoryId={id}` - جلب فئات فرعية لفئة معينة
- `POST /api/admin/subcategories` - إضافة فئة فرعية جديدة
- `PUT /api/admin/subcategories?id={id}` - تحديث فئة فرعية
- `DELETE /api/admin/subcategories?id={id}` - حذف فئة فرعية

### المنتجات
- `GET /api/admin/products` - جلب جميع المنتجات
- `GET /api/admin/products?categoryId={id}` - جلب منتجات فئة معينة
- `GET /api/admin/products?subcategoryId={id}` - جلب منتجات فئة فرعية معينة
- `POST /api/admin/products` - إضافة منتج جديد
- `PUT /api/admin/products?id={id}` - تحديث منتج
- `DELETE /api/admin/products?id={id}` - حذف منتج

### الإعدادات
- `GET /api/admin/settings` - جلب إعدادات الموقع
- `PUT /api/admin/settings` - تحديث إعدادات الموقع

### الإحصائيات
- `GET /api/admin/stats` - جلب إحصائيات الموقع

## 🛠️ دوال قاعدة البيانات

### دوال الفئات
- `getCategories()` - جلب جميع الفئات
- `getCategoryById(id)` - جلب فئة بالمعرف
- `addCategory(category)` - إضافة فئة جديدة
- `updateCategory(id, updates)` - تحديث فئة
- `deleteCategory(id)` - حذف فئة

### دوال الفئات الفرعية
- `getSubcategories()` - جلب جميع الفئات الفرعية
- `getSubcategoriesByCategory(categoryId)` - جلب فئات فرعية لفئة معينة
- `addSubcategory(subcategory)` - إضافة فئة فرعية
- `updateSubcategory(id, updates)` - تحديث فئة فرعية
- `deleteSubcategory(id)` - حذف فئة فرعية

### دوال المنتجات
- `getProducts()` - جلب جميع المنتجات
- `getProductsByCategory(categoryId)` - جلب منتجات فئة معينة
- `addProduct(product)` - إضافة منتج جديد
- `updateProduct(id, updates)` - تحديث منتج
- `deleteProduct(id)` - حذف منتج

## 🔄 كيفية عمل النظام

### 1. قراءة البيانات
```typescript
const categories = await categoriesAPI.getAll();
```

### 2. إضافة بيانات جديدة
```typescript
const newCategory = await categoriesAPI.create({
  name: "New Category",
  nameAr: "فئة جديدة",
  description: "Description",
  descriptionAr: "وصف",
  image: "",
  isActive: true
});
```

### 3. تحديث البيانات
```typescript
const updatedCategory = await categoriesAPI.update(categoryId, {
  name: "Updated Name",
  isActive: false
});
```

### 4. حذف البيانات
```typescript
await categoriesAPI.delete(categoryId);
```

## 🔒 الأمان والتحقق

### التحقق من البيانات
- التحقق من الحقول المطلوبة
- التحقق من صحة البيانات
- معالجة الأخطاء

### الحماية
- التحقق من وجود البيانات قبل التحديث/الحذف
- حذف البيانات المرتبطة عند حذف فئة رئيسية
- معالجة الأخطاء بشكل آمن

## 📊 المميزات

### ✅ المميزات المتاحة
- **إضافة ديناميكية** للفئات والمنتجات
- **تعديل فوري** للبيانات
- **حذف آمن** مع حذف البيانات المرتبطة
- **إحصائيات تلقائية** محدثة
- **واجهة سهلة** للإدارة
- **معالجة أخطاء** شاملة
- **تحميل تفاعلي** مع مؤشرات

### 🔄 التحديثات التلقائية
- تحديث الإحصائيات عند تغيير البيانات
- إعادة تحميل البيانات بعد العمليات
- تحديث الواجهة فوراً

## 🚀 الاستخدام

### في لوحة التحكم
1. **الفئات الرئيسية:** إضافة وتعديل وحذف الفئات
2. **الفئات الفرعية:** إدارة الفئات الفرعية المرتبطة
3. **المنتجات:** إدارة شاملة للمنتجات
4. **الإعدادات:** تحديث إعدادات الموقع
5. **الإحصائيات:** عرض إحصائيات محدثة

### في الموقع الرئيسي
- عرض البيانات المحدثة فوراً
- فلترة حسب الفئات والفئات الفرعية
- عرض المنتجات بالتفاصيل الكاملة

## 🔮 التطوير المستقبلي

### إمكانيات إضافية
- **قاعدة بيانات حقيقية** (MySQL, PostgreSQL)
- **رفع الصور** للخادم
- **نسخ احتياطية** تلقائية
- **تاريخ التغييرات** (Version Control)
- **صلاحيات متقدمة** للمستخدمين
- **API خارجي** للتطبيقات الأخرى

---

**النظام جاهز للاستخدام ويوفر إدارة شاملة وديناميكية لجميع بيانات الموقع** 🎉
