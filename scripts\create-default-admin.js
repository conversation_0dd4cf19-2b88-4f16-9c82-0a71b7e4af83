const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');

// إعدادات قاعدة البيانات
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'droobhajer_db',
  charset: 'utf8mb4'
};

async function createDefaultAdmin() {
  let connection = null;
  
  try {
    console.log('🔄 الاتصال بقاعدة البيانات...');
    connection = await mysql.createConnection(dbConfig);
    
    // التحقق من وجود مستخدم افتراضي
    const [existingUsers] = await connection.execute(
      'SELECT COUNT(*) as count FROM admins WHERE username = ?',
      ['admin']
    );
    
    if (existingUsers[0].count > 0) {
      console.log('✅ المستخدم الافتراضي موجود بالفعل');
      return;
    }
    
    // إنشاء كلمة مرور مشفرة
    const defaultPassword = 'admin123';
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(defaultPassword, saltRounds);
    
    // إدراج المستخدم الافتراضي
    await connection.execute(
      `INSERT INTO admins (username, email, password_hash, is_active, created_at, updated_at) 
       VALUES (?, ?, ?, 1, NOW(), NOW())`,
      ['admin', '<EMAIL>', passwordHash]
    );
    
    console.log('✅ تم إنشاء المستخدم الافتراضي بنجاح!');
    console.log('📋 بيانات تسجيل الدخول:');
    console.log('   اسم المستخدم: admin');
    console.log('   كلمة المرور: admin123');
    console.log('   البريد الإلكتروني: <EMAIL>');
    console.log('⚠️  يرجى تغيير كلمة المرور بعد أول تسجيل دخول!');
    
  } catch (error) {
    console.error('❌ خطأ في إنشاء المستخدم الافتراضي:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// تشغيل الدالة
createDefaultAdmin();
