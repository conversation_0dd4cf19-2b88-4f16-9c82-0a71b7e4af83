// إدارة الجلسات في المتصفح بدون قاعدة بيانات

// إنشاء معرف جلسة فريد
export function generateSessionId(): string {
  return 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now().toString(36);
}

// الحصول على معرف الجلسة الحالي أو إنشاء جديد
export function getSessionId(): string {
  if (typeof window === 'undefined') return ''; // للـ SSR

  // محاولة الحصول على الجلسة من الكوكيز أولاً
  let sessionId = getCookie('sessionId');

  // إذا لم توجد في الكوكيز، جرب localStorage
  if (!sessionId) {
    sessionId = localStorage.getItem('currentSessionId');
  }

  // إذا لم توجد في أي مكان، أنشئ جديدة
  if (!sessionId) {
    sessionId = generateSessionId();
    setCookie('sessionId', sessionId, 7); // 7 أيام
    localStorage.setItem('currentSessionId', sessionId);
    console.log('🆕 تم إنشاء جلسة جديدة:', sessionId);
  } else if (!getCookie('sessionId')) {
    // إذا كانت موجودة في localStorage فقط، احفظها في الكوكيز أيضاً
    setCookie('sessionId', sessionId, 7);
  }

  return sessionId;
}

// دوال مساعدة للكوكيز
function getCookie(name: string): string | null {
  if (typeof document === 'undefined') return null;

  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
  return null;
}

function setCookie(name: string, value: string, days: number): void {
  if (typeof document === 'undefined') return;

  const expires = new Date();
  expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
  document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/;SameSite=Strict`;
}

// إنشاء مفتاح localStorage مرتبط بالجلسة
export function getSessionKey(key: string): string {
  const sessionId = getSessionId();
  return `${key}_${sessionId}`;
}

// حفظ البيانات في localStorage مرتبطة بالجلسة
export function setSessionData(key: string, data: any): void {
  if (typeof window === 'undefined') return;
  
  const sessionKey = getSessionKey(key);
  localStorage.setItem(sessionKey, JSON.stringify(data));
}

// جلب البيانات من localStorage مرتبطة بالجلسة
export function getSessionData<T>(key: string, defaultValue: T): T {
  if (typeof window === 'undefined') return defaultValue;
  
  try {
    const sessionKey = getSessionKey(key);
    const data = localStorage.getItem(sessionKey);
    return data ? JSON.parse(data) : defaultValue;
  } catch (error) {
    console.error('خطأ في جلب بيانات الجلسة:', error);
    return defaultValue;
  }
}

// حذف البيانات من localStorage مرتبطة بالجلسة
export function removeSessionData(key: string): void {
  if (typeof window === 'undefined') return;
  
  const sessionKey = getSessionKey(key);
  localStorage.removeItem(sessionKey);
}

// تنظيف جميع بيانات الجلسة الحالية
export function clearCurrentSession(): void {
  if (typeof window === 'undefined') return;
  
  const sessionId = getSessionId();
  const keysToRemove: string[] = [];
  
  // البحث عن جميع المفاتيح المرتبطة بالجلسة الحالية
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && key.endsWith(`_${sessionId}`)) {
      keysToRemove.push(key);
    }
  }
  
  // حذف جميع البيانات المرتبطة بالجلسة
  keysToRemove.forEach(key => localStorage.removeItem(key));
  
  console.log(`🧹 تم تنظيف ${keysToRemove.length} عنصر من الجلسة ${sessionId}`);
}

// إنشاء جلسة جديدة (مفيد عند تسجيل الخروج أو البدء من جديد)
export function createNewSession(): string {
  if (typeof window === 'undefined') return '';
  
  // تنظيف الجلسة الحالية
  clearCurrentSession();
  
  // إنشاء جلسة جديدة
  const newSessionId = generateSessionId();
  localStorage.setItem('currentSessionId', newSessionId);
  
  console.log('🔄 تم إنشاء جلسة جديدة:', newSessionId);
  return newSessionId;
}

// تنظيف الجلسات القديمة (اختياري - لتوفير مساحة)
export function cleanOldSessions(): void {
  if (typeof window === 'undefined') return;
  
  const currentSessionId = getSessionId();
  const keysToRemove: string[] = [];
  
  // البحث عن جميع المفاتيح التي تحتوي على session_
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && key.includes('_session_') && !key.endsWith(`_${currentSessionId}`)) {
      keysToRemove.push(key);
    }
  }
  
  // حذف الجلسات القديمة
  keysToRemove.forEach(key => localStorage.removeItem(key));
  
  if (keysToRemove.length > 0) {
    console.log(`🗑️ تم تنظيف ${keysToRemove.length} عنصر من الجلسات القديمة`);
  }
}

// Hook React لاستخدام بيانات الجلسة (يحتاج import React)
import { useState, useEffect } from 'react';

export function useSessionStorage<T>(key: string, defaultValue: T): [T, (value: T) => void] {
  if (typeof window === 'undefined') {
    return [defaultValue, () => {}];
  }

  const [data, setData] = useState<T>(() => getSessionData(key, defaultValue));

  const setSessionValue = (value: T) => {
    setSessionData(key, value);
    setData(value);

    // إرسال event للمكونات الأخرى
    window.dispatchEvent(new CustomEvent(`sessionStorage_${key}`, { detail: value }));
  };

  useEffect(() => {
    const handleStorageChange = (e: CustomEvent) => {
      setData(e.detail);
    };

    window.addEventListener(`sessionStorage_${key}` as any, handleStorageChange);

    return () => {
      window.removeEventListener(`sessionStorage_${key}` as any, handleStorageChange);
    };
  }, [key]);

  return [data, setSessionValue];
}

// معلومات الجلسة للتطوير
export function getSessionInfo() {
  if (typeof window === 'undefined') return null;
  
  const sessionId = getSessionId();
  const sessionKeys: string[] = [];
  
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && key.endsWith(`_${sessionId}`)) {
      sessionKeys.push(key);
    }
  }
  
  return {
    sessionId,
    keysCount: sessionKeys.length,
    keys: sessionKeys
  };
}
