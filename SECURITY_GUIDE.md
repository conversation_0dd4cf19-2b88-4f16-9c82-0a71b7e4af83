# 🔐 دليل الأمان - مشروع دروب هاجر

## ✅ التحسينات الأمنية المطبقة

### 1. نظام المصادقة الآمن
- **تشفير كلمات المرور**: استخدام bcrypt مع salt rounds = 12
- **JWT Tokens**: مع انتهاء صلاحية 24 ساعة
- **Rate Limiting**: حماية من هجمات القوة الغاشمة (5 محاولات / 15 دقيقة)
- **Secure Cookies**: HttpOnly, Secure, SameSite

### 2. حماية البيانات
- **تشفير البيانات الحساسة**: استخدام AES encryption
- **إزالة البيانات المكشوفة**: من الكود المصدري
- **تخزين آمن**: للمعلومات الإدارية

### 3. حماية API Routes
- **مصادقة إجبارية**: لجميع العمليات الإدارية
- **التحقق من الصلاحيات**: قبل كل عملية
- **معالجة آمنة للأخطاء**: بدون كشف معلومات حساسة

## 🚨 إعدادات مطلوبة قبل النشر

### 1. متغيرات البيئة الإنتاجية
```bash
# يجب تغيير هذه القيم في الإنتاج
JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters-long
ENCRYPTION_KEY=your-encryption-key-minimum-32-characters-long
DEFAULT_ADMIN_PASSWORD=YourSecurePassword@2024!

# إعدادات الإيميل الحقيقية
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-real-app-password
ADMIN_EMAIL=<EMAIL>
```

### 2. كلمة المرور الافتراضية
- **كلمة المرور الحالية**: `SecureAdmin@2024!`
- **يجب تغييرها فوراً** بعد أول تسجيل دخول
- **الوصول**: `/admin/login`

### 3. إعدادات الخادم
```nginx
# إضافة headers الأمان في Nginx
add_header X-Frame-Options DENY;
add_header X-Content-Type-Options nosniff;
add_header X-XSS-Protection "1; mode=block";
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
```

## 📁 الملفات الآمنة الجديدة

### ملفات المصادقة
- `src/lib/auth.ts` - نظام المصادقة الأساسي
- `src/lib/secure-storage.ts` - تخزين آمن للبيانات
- `src/pages/api/auth/login.ts` - API تسجيل الدخول
- `src/pages/api/auth/logout.ts` - API تسجيل الخروج
- `src/pages/api/auth/verify.ts` - API التحقق من المصادقة

### البيانات المحمية
- `data/secure-admin.enc` - ملف البيانات المشفر (سيتم إنشاؤه تلقائياً)

## 🔄 كيفية استخدام النظام الجديد

### 1. تسجيل الدخول
```javascript
// استخدام API الجديد
const response = await fetch('/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ username, password })
});
```

### 2. التحقق من المصادقة
```javascript
// التحقق من حالة المصادقة
const response = await fetch('/api/auth/verify');
const { user } = await response.json();
```

### 3. تسجيل الخروج
```javascript
// تسجيل الخروج الآمن
await fetch('/api/auth/logout', { method: 'POST' });
```

## ⚠️ تحذيرات مهمة

### 1. الملفات المحذوفة/المعدلة
- **تم إزالة**: البيانات الحساسة من `src/data/settings.ts`
- **تم تحديث**: جميع API routes لتتطلب مصادقة
- **تم حماية**: رفع الملفات بمصادقة إجبارية

### 2. التوافق مع الكود القديم
- الدوال القديمة ستعرض تحذيرات
- يجب تحديث الواجهة الأمامية لاستخدام النظام الجديد
- localStorage لم يعد يُستخدم للمصادقة

### 3. النسخ الاحتياطية
- احتفظ بنسخة احتياطية قبل النشر
- تأكد من عمل النظام الجديد محلياً أولاً

## 🔧 خطوات ما بعد النشر

### 1. فوراً بعد النشر
1. غيّر كلمة مرور الإدارة
2. تحقق من عمل جميع الوظائف
3. راقب logs الأخطاء

### 2. خلال أسبوع
1. حدّث الواجهة الأمامية للنظام الجديد
2. أزل الدوال القديمة المهجورة
3. أضف المزيد من headers الأمان

### 3. مراقبة مستمرة
1. راقب محاولات تسجيل الدخول المشبوهة
2. تحقق من logs الأمان
3. حدّث كلمات المرور دورياً

## 📞 الدعم
في حالة وجود مشاكل أمنية، تواصل مع فريق التطوير فوراً.

## 🚀 دليل النشر الآمن

### خطوات النشر المطلوبة

#### 1. قبل النشر
```bash
# فحص الأمان
npm run security-check

# بناء آمن
npm run build-secure

# نشر آمن (يتضمن جميع الفحوصات)
npm run deploy
```

#### 2. إعداد الخادم
1. **نسخ ملف البيئة**:
   ```bash
   cp .env.production.example .env.production
   # قم بتعديل القيم في .env.production
   ```

2. **تثبيت التبعيات**:
   ```bash
   npm ci --production
   ```

3. **تشغيل المشروع**:
   ```bash
   npm start
   ```

#### 3. فحص ما بعد النشر
- [ ] تسجيل الدخول للوحة الإدارة يعمل
- [ ] رفع الملفات يعمل بشكل آمن
- [ ] جميع API routes محمية
- [ ] Headers الأمان موجودة
- [ ] Rate limiting يعمل
- [ ] قاعدة البيانات مشفرة

### 🔧 Scripts المتاحة

| Script | الوصف |
|--------|--------|
| `npm run security-check` | فحص الأمان الشامل |
| `npm run migrate-uploads` | ترحيل الملفات للنظام الآمن |
| `npm run migrate-database` | ترحيل قاعدة البيانات للنظام المشفر |
| `npm run build-secure` | بناء آمن مع فحص الأمان |
| `npm run deploy` | نشر آمن شامل |

### ⚠️ تحذيرات مهمة للنشر

1. **غيّر جميع كلمات المرور الافتراضية**
2. **استخدم HTTPS في الإنتاج**
3. **فعّل جدار الحماية**
4. **راقب logs الأمان**
5. **أنشئ نسخ احتياطية دورية**

---
**آخر تحديث**: 2024-12-20
**الإصدار**: 2.0 - النظام الآمن المكتمل
