// اختبار بسيط لنظام إدارة المنتجات
// يمكن تشغيله من خلال Node.js لاختبار الدوال

const testProductData = {
  product: {
    id: 'test-product-' + Date.now(),
    title: 'Test Product',
    title_ar: 'منتج تجريبي',
    description: 'This is a test product description',
    description_ar: 'هذا وصف منتج تجريبي',
    price: 100.50,
    original_price: 150.00,
    is_available: true,
    category_id: 'test-category',
    subcategory_id: 'test-subcategory',
    is_active: true,
    is_featured: false
  },
  images: [
    'https://example.com/image1.jpg',
    'https://example.com/image2.jpg',
    'https://example.com/image3.jpg'
  ],
  features: [
    { text: 'High Quality', textAr: 'جودة عالية' },
    { text: 'Durable', textAr: 'متين' },
    { text: 'Easy to Clean', textAr: 'سهل التنظيف' }
  ],
  specifications: [
    { key: 'Material', keyAr: 'المادة', value: 'Stainless Steel', valueAr: 'فولاذ مقاوم للصدأ' },
    { key: 'Size', keyAr: 'الحجم', value: 'Large', valueAr: 'كبير' },
    { key: 'Color', keyAr: 'اللون', value: 'Silver', valueAr: 'فضي' }
  ]
};

// اختبار إضافة منتج
async function testAddProduct() {
  try {
    console.log('🧪 اختبار إضافة منتج...');
    
    const response = await fetch('http://localhost:3000/api/admin/products', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // يجب إضافة رمز المصادقة هنا في الاختبار الحقيقي
      },
      body: JSON.stringify({
        title: testProductData.product.title,
        titleAr: testProductData.product.title_ar,
        description: testProductData.product.description,
        descriptionAr: testProductData.product.description_ar,
        images: testProductData.images,
        price: testProductData.product.price,
        originalPrice: testProductData.product.original_price,
        available: testProductData.product.is_available,
        categoryId: testProductData.product.category_id,
        subcategoryId: testProductData.product.subcategory_id,
        features: testProductData.features.map(f => f.text),
        featuresAr: testProductData.features.map(f => f.textAr),
        specifications: testProductData.specifications.map(spec => ({
          nameEn: spec.key,
          nameAr: spec.keyAr,
          valueEn: spec.value,
          valueAr: spec.valueAr
        })),
        isActive: testProductData.product.is_active,
        isFeatured: testProductData.product.is_featured
      })
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✅ تم إضافة المنتج بنجاح:', result.data.id);
      return result.data.id;
    } else {
      console.error('❌ فشل في إضافة المنتج:', response.status);
      return null;
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار إضافة المنتج:', error.message);
    return null;
  }
}

// اختبار جلب منتج
async function testGetProduct(productId) {
  try {
    console.log('🧪 اختبار جلب منتج...');
    
    const response = await fetch(`http://localhost:3000/api/admin/products?id=${productId}`);
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ تم جلب المنتج بنجاح');
      console.log('📊 عدد الصور:', result.data.images.length);
      console.log('📊 عدد المميزات:', result.data.features.length);
      console.log('📊 عدد المواصفات:', result.data.specifications.length);
      return true;
    } else {
      console.error('❌ فشل في جلب المنتج:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار جلب المنتج:', error.message);
    return false;
  }
}

// اختبار تحديث منتج
async function testUpdateProduct(productId) {
  try {
    console.log('🧪 اختبار تحديث منتج...');
    
    const response = await fetch(`http://localhost:3000/api/admin/products?id=${productId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        title: 'Updated Test Product',
        titleAr: 'منتج تجريبي محدث',
        price: 120.00,
        isFeatured: true
      })
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✅ تم تحديث المنتج بنجاح');
      return true;
    } else {
      console.error('❌ فشل في تحديث المنتج:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار تحديث المنتج:', error.message);
    return false;
  }
}

// اختبار حذف منتج
async function testDeleteProduct(productId) {
  try {
    console.log('🧪 اختبار حذف منتج...');
    
    const response = await fetch(`http://localhost:3000/api/admin/products?id=${productId}`, {
      method: 'DELETE'
    });

    if (response.ok) {
      console.log('✅ تم حذف المنتج بنجاح');
      return true;
    } else {
      console.error('❌ فشل في حذف المنتج:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في اختبار حذف المنتج:', error.message);
    return false;
  }
}

// تشغيل جميع الاختبارات
async function runAllTests() {
  console.log('🚀 بدء اختبار نظام إدارة المنتجات المتكامل\n');
  
  // اختبار إضافة منتج
  const productId = await testAddProduct();
  if (!productId) {
    console.log('❌ فشل الاختبار - لا يمكن المتابعة');
    return;
  }
  
  // اختبار جلب منتج
  const getSuccess = await testGetProduct(productId);
  if (!getSuccess) {
    console.log('❌ فشل اختبار جلب المنتج');
  }
  
  // اختبار تحديث منتج
  const updateSuccess = await testUpdateProduct(productId);
  if (!updateSuccess) {
    console.log('❌ فشل اختبار تحديث المنتج');
  }
  
  // اختبار حذف منتج
  const deleteSuccess = await testDeleteProduct(productId);
  if (!deleteSuccess) {
    console.log('❌ فشل اختبار حذف المنتج');
  }
  
  console.log('\n🏁 انتهاء الاختبارات');
}

// تصدير الدوال للاستخدام
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testAddProduct,
    testGetProduct,
    testUpdateProduct,
    testDeleteProduct,
    runAllTests
  };
}

// تشغيل الاختبارات إذا تم تشغيل الملف مباشرة
if (typeof window === 'undefined' && require.main === module) {
  runAllTests();
}
