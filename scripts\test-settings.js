const fs = require('fs');
const path = require('path');

// مسار ملف الإعدادات
const settingsPath = path.join(__dirname, '..', 'src', 'data', 'settings.json');

console.log('🔍 اختبار نظام الإعدادات...');
console.log('📁 مسار ملف الإعدادات:', settingsPath);

// التحقق من وجود ملف الإعدادات
if (fs.existsSync(settingsPath)) {
  console.log('✅ ملف الإعدادات موجود');
  
  try {
    const settingsContent = fs.readFileSync(settingsPath, 'utf8');
    const settings = JSON.parse(settingsContent);
    
    console.log('📋 محتوى الإعدادات الحالية:');
    console.log('   اسم الموقع:', settings.siteName || 'غير محدد');
    console.log('   اسم الموقع بالعربية:', settings.siteNameAr || 'غير محدد');
    console.log('   البريد الإلكتروني:', settings.contactEmail || 'غير محدد');
    console.log('   رقم الواتساب:', settings.whatsappNumber || 'غير محدد');
    
    // اختبار تحديث الإعدادات
    console.log('\n🔄 اختبار تحديث الإعدادات...');
    
    const testSettings = {
      ...settings,
      siteName: 'VidMeet Test',
      siteNameAr: 'فيد ميت تجريبي',
      contactEmail: '<EMAIL>',
      lastUpdated: new Date().toISOString()
    };
    
    fs.writeFileSync(settingsPath, JSON.stringify(testSettings, null, 2));
    console.log('✅ تم تحديث الإعدادات بنجاح');
    
    // التحقق من التحديث
    const updatedContent = fs.readFileSync(settingsPath, 'utf8');
    const updatedSettings = JSON.parse(updatedContent);
    
    console.log('📋 الإعدادات بعد التحديث:');
    console.log('   اسم الموقع:', updatedSettings.siteName);
    console.log('   اسم الموقع بالعربية:', updatedSettings.siteNameAr);
    console.log('   البريد الإلكتروني:', updatedSettings.contactEmail);
    console.log('   آخر تحديث:', updatedSettings.lastUpdated);
    
    // إعادة الإعدادات الأصلية
    fs.writeFileSync(settingsPath, JSON.stringify(settings, null, 2));
    console.log('🔄 تم إعادة الإعدادات الأصلية');
    
  } catch (error) {
    console.error('❌ خطأ في قراءة أو تحديث الإعدادات:', error.message);
  }
  
} else {
  console.log('❌ ملف الإعدادات غير موجود');
  console.log('💡 سيتم إنشاء ملف إعدادات افتراضي...');
  
  const defaultSettings = {
    siteName: 'VidMeet',
    siteNameAr: 'فيد ميت',
    contactEmail: '<EMAIL>',
    whatsappNumber: '+966501234567',
    socialLinks: {
      facebook: 'https://facebook.com/droobhajer',
      instagram: 'https://instagram.com/droobhajer',
      twitter: 'https://twitter.com/droobhajer',
      linkedin: 'https://linkedin.com/company/droobhajer',
      youtube: 'https://youtube.com/@droobhajer'
    },
    createdAt: new Date().toISOString()
  };
  
  try {
    // إنشاء المجلد إذا لم يكن موجوداً
    const settingsDir = path.dirname(settingsPath);
    if (!fs.existsSync(settingsDir)) {
      fs.mkdirSync(settingsDir, { recursive: true });
    }
    
    fs.writeFileSync(settingsPath, JSON.stringify(defaultSettings, null, 2));
    console.log('✅ تم إنشاء ملف الإعدادات الافتراضي');
  } catch (error) {
    console.error('❌ خطأ في إنشاء ملف الإعدادات:', error.message);
  }
}

console.log('\n🎯 نصائح لاختبار النظام:');
console.log('1. تأكد من تشغيل الخادم: npm run dev');
console.log('2. اذهب إلى: http://localhost:3000/admin/settings');
console.log('3. قم بتغيير أي إعداد واحفظ');
console.log('4. تحقق من انعكاس التغييرات على الموقع');
console.log('5. تحقق من ملف الإعدادات:', settingsPath);
