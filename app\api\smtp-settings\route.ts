import { NextRequest, NextResponse } from 'next/server';
import { getContactInfo, updateContactInfo } from '@/lib/mysql-database';

// GET - جلب إعدادات SMTP
export async function GET() {
  try {
    const contactInfo = await getContactInfo();

    // إرجاع الإعدادات مع API Key للتشخيص
    return NextResponse.json({
      success: true,
      settings: {
        email: contactInfo?.email || '',
        hasPassword: !!contactInfo?.Password,
        apiKey: contactInfo?.Password || 'غير موجود',
        apiType: 'resend',
        service: 'Resend API'
      }
    });
  } catch (error) {
    console.error('Error fetching SMTP settings:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch SMTP settings'
    }, { status: 500 });
  }
}

// POST - تحديث إعدادات SMTP
export async function POST(request: NextRequest) {
    try {
    const body = await request.json();
    const { email, password } = body;

    if (!email || !password) {
      return NextResponse.json({
        success: false,
        error: 'Email and password are required'
      }, { status: 400 });
    }

      // تحديث إعدادات SMTP في قاعدة البيانات
      await updateContactInfo(email, password);

    return NextResponse.json({
      success: true,
      message: 'SMTP settings updated successfully'
    });
  } catch (error) {
    console.error('Error updating SMTP settings:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to update SMTP settings'
    }, { status: 500 });
  }
}
