# نظام إدارة طلبات التسعير

تم إضافة نظام شامل لإدارة طلبات التسعير إلى موقع VidMeet. يتيح هذا النظام للعملاء طلب عروض أسعار للمنتجات المختارة، وللإدارة متابعة ومعالجة هذه الطلبات.

## المميزات الجديدة

### 1. للعملاء
- **طلب عرض سعر من السلة**: يمكن للعملاء طلب عرض سعر للمنتجات الموجودة في السلة
- **نموذج معلومات العميل**: إدخال الاسم، الإيميل، رقم الهاتف، واسم الشركة (اختياري)
- **حفظ تلقائي للبيانات**: يتم حفظ بيانات العميل والمنتجات تلقائياً

### 2. للإدارة
- **لوحة تحكم محدثة**: عرض إحصائيات طلبات التسعير في الداشبورد الرئيسي
- **صفحة مخصصة لطلبات التسعير**: عرض جميع الطلبات مع تفاصيل كاملة
- **تصدير إلى Excel**: ملف Excel لكل طلب يحتوي على المنتجات المطلوبة
- **أدوات التواصل**: أزرار مباشرة للإيميل والواتساب
- **إدارة الحالات**: تحديث حالة الطلب (في الانتظار، تم المعالجة، تم الإرسال)
- **إعدادات المراسلة**: حفظ بيانات الشركة للمراسلة

## الملفات المضافة/المحدثة

### API Endpoints
- `src/pages/api/quote-requests.ts` - إنشاء وجلب طلبات التسعير
- `src/pages/api/quote-requests/[id].ts` - تحديث طلب تسعير محدد
- `src/pages/api/download-excel/[filename].ts` - تحميل ملفات Excel

### صفحات الإدارة
- `src/pages/admin/quote-requests.tsx` - صفحة إدارة طلبات التسعير
- `src/pages/admin/dashboard.tsx` - محدث لعرض إحصائيات طلبات التسعير

### صفحات العملاء
- `src/pages/cart.tsx` - محدث لإرسال طلبات التسعير

### مكونات الإدارة
- `src/components/admin/AdminLayout.tsx` - محدث لإضافة رابط طلبات التسعير

### أنواع البيانات
- `src/types/admin.ts` - محدث لإضافة أنواع طلبات التسعير

## كيفية الاستخدام

### للعملاء
1. إضافة المنتجات إلى السلة
2. الذهاب إلى صفحة السلة
3. النقر على "طلب عرض سعر"
4. ملء نموذج معلومات العميل
5. إرسال الطلب

### للإدارة
1. تسجيل الدخول إلى لوحة التحكم
2. الذهاب إلى "طلبات التسعير" من القائمة الجانبية
3. عرض جميع الطلبات مع تفاصيلها
4. تحميل ملف Excel للطلب المحدد
5. إضافة الأسعار في ملف Excel
6. إرسال العرض عبر الإيميل أو الواتساب
7. تحديث حالة الطلب

## بنية البيانات

### طلب التسعير
```json
{
  "id": "QR-1234567890-abc123",
  "customerInfo": {
    "name": "اسم العميل",
    "email": "<EMAIL>",
    "phone": "966501234567",
    "company": "اسم الشركة"
  },
  "products": [
    {
      "id": "product-1",
      "title": "Product Name",
      "titleAr": "اسم المنتج",
      "price": 100,
      "quantity": 2,
      "image": "product-image.jpg"
    }
  ],
  "totalAmount": 200,
  "createdAt": "2024-01-01T00:00:00.000Z",
  "status": "pending",
  "excelFilePath": "src/data/excel/QR-1234567890-abc123.xlsx"
}
```

### ملف Excel
يحتوي ملف Excel على الأعمدة التالية:
- رقم المنتج
- اسم المنتج
- الكمية
- السعر المقترح (فارغ للملء من قبل الإدارة)
- ملاحظات (فارغ للملء من قبل الإدارة)

## مجلدات البيانات

### `src/data/quotes/`
- `index.json` - فهرس جميع طلبات التسعير
- `QR-[ID].json` - ملف منفصل لكل طلب تسعير

### `src/data/excel/`
- `QR-[ID].xlsx` - ملف Excel لكل طلب تسعير

## الحالات المختلفة

### `pending` - في الانتظار
- الطلب جديد ولم يتم معالجته بعد
- لون أصفر في الواجهة

### `processed` - تم المعالجة
- تم مراجعة الطلب وإضافة الأسعار
- لون أزرق في الواجهة

### `sent` - تم الإرسال
- تم إرسال العرض للعميل
- لون أخضر في الواجهة

## إعدادات المراسلة

يمكن للإدارة حفظ بيانات الشركة للمراسلة:
- إيميل الشركة
- رقم واتساب الشركة

هذه البيانات محفوظة في localStorage ويمكن تحديثها من خلال مودال الإعدادات.

## المتطلبات التقنية

### المكتبات المضافة
- `xlsx` - لإنشاء ملفات Excel

### المتطلبات
- Node.js
- Next.js
- React
- TypeScript

## الأمان

- جميع طلبات API محمية
- التحقق من صحة البيانات المدخلة
- حفظ آمن للملفات في مجلدات محددة

## التطوير المستقبلي

يمكن إضافة المميزات التالية:
- إشعارات فورية للطلبات الجديدة
- تصدير تقارير شهرية
- ربط مع أنظمة CRM خارجية
- إرسال إيميلات تلقائية
- تتبع حالة الطلب للعملاء
