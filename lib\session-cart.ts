import { getSessionData, setSessionData, removeSessionData } from './browser-session';

export interface CartItem {
  id: string;
  title: string;
  titleAr?: string;
  image: string;
  price: number;
  quantity: number;
}

const CART_KEY = 'cart';

// جلب عربة التسوق من الجلسة
export function getCart(): CartItem[] {
  return getSessionData<CartItem[]>(CART_KEY, []);
}

// حفظ عربة التسوق في الجلسة
export function saveCart(items: CartItem[]): void {
  setSessionData(CART_KEY, items);
  // إرسال event للمكونات الأخرى
  if (typeof window !== 'undefined') {
    window.dispatchEvent(new Event('cartUpdated'));
  }
}

// إضافة منتج إلى العربة
export function addToCart(item: Omit<CartItem, 'quantity'> & { quantity?: number }): CartItem[] {
  const cart = getCart();
  const existingItemIndex = cart.findIndex(cartItem => cartItem.id === item.id);
  
  if (existingItemIndex > -1) {
    // إذا كان المنتج موجود، زيادة الكمية
    cart[existingItemIndex].quantity += item.quantity || 1;
  } else {
    // إذا لم يكن موجود، إضافته للعربة
    cart.push({
      ...item,
      quantity: item.quantity || 1
    });
  }
  
  saveCart(cart);
  console.log(`✅ تم إضافة ${item.title} إلى العربة`);
  return cart;
}

// تحديث كمية منتج في العربة
export function updateCartItemQuantity(productId: string, newQuantity: number): CartItem[] {
  const cart = getCart();
  
  if (newQuantity <= 0) {
    return removeFromCart(productId);
  }
  
  const itemIndex = cart.findIndex(item => item.id === productId);
  if (itemIndex > -1) {
    cart[itemIndex].quantity = newQuantity;
    saveCart(cart);
    console.log(`🔄 تم تحديث كمية المنتج ${cart[itemIndex].title} إلى ${newQuantity}`);
  }
  
  return cart;
}

// حذف منتج من العربة
export function removeFromCart(productId: string): CartItem[] {
  const cart = getCart();
  const filteredCart = cart.filter(item => item.id !== productId);
  
  if (filteredCart.length !== cart.length) {
    saveCart(filteredCart);
    console.log(`🗑️ تم حذف المنتج من العربة`);
  }
  
  return filteredCart;
}

// تفريغ العربة بالكامل
export function clearCart(): void {
  removeSessionData(CART_KEY);
  if (typeof window !== 'undefined') {
    window.dispatchEvent(new Event('cartUpdated'));
  }
  console.log('🧹 تم تفريغ العربة بالكامل');
}

// الحصول على عدد المنتجات في العربة
export function getCartItemCount(): number {
  const cart = getCart();
  return cart.reduce((total, item) => total + item.quantity, 0);
}

// الحصول على إجمالي سعر العربة
export function getCartTotal(): number {
  const cart = getCart();
  return cart.reduce((total, item) => total + (item.price * item.quantity), 0);
}

// التحقق من وجود منتج في العربة
export function isProductInCart(productId: string): boolean {
  const cart = getCart();
  return cart.some(item => item.id === productId);
}

// الحصول على منتج محدد من العربة
export function getCartItem(productId: string): CartItem | undefined {
  const cart = getCart();
  return cart.find(item => item.id === productId);
}

// ترحيل البيانات من localStorage العادي إلى الجلسة (للمستخدمين الحاليين)
export function migrateFromOldLocalStorage(): boolean {
  if (typeof window === 'undefined') return false;
  
  try {
    // محاولة جلب البيانات من localStorage العادي
    const oldCartData = localStorage.getItem('cart');
    
    if (oldCartData) {
      const oldCart: CartItem[] = JSON.parse(oldCartData);
      
      if (Array.isArray(oldCart) && oldCart.length > 0) {
        // حفظ البيانات في الجلسة الجديدة
        saveCart(oldCart);
        
        // حذف البيانات القديمة
        localStorage.removeItem('cart');
        
        console.log(`🔄 تم ترحيل ${oldCart.length} منتج من localStorage العادي إلى الجلسة`);
        return true;
      }
    }
    
    return false;
  } catch (error) {
    console.error('خطأ في ترحيل البيانات:', error);
    return false;
  }
}

// Hook React لاستخدام عربة التسوق
import { useState, useEffect } from 'react';

export function useCart(): {
  cart: CartItem[];
  addToCart: (item: Omit<CartItem, 'quantity'> & { quantity?: number }) => void;
  updateQuantity: (productId: string, quantity: number) => void;
  removeFromCart: (productId: string) => void;
  clearCart: () => void;
  itemCount: number;
  total: number;
  isProductInCart: (productId: string) => boolean;
} {
  const [cart, setCart] = useState<CartItem[]>([]);

  // تحديث العربة عند التحميل
  useEffect(() => {
    setCart(getCart());
    
    // محاولة ترحيل البيانات القديمة
    migrateFromOldLocalStorage();
  }, []);

  // الاستماع لتحديثات العربة
  useEffect(() => {
    const handleCartUpdate = () => {
      setCart(getCart());
    };

    window.addEventListener('cartUpdated', handleCartUpdate);
    
    return () => {
      window.removeEventListener('cartUpdated', handleCartUpdate);
    };
  }, []);

  return {
    cart,
    addToCart: (item) => {
      const updatedCart = addToCart(item);
      setCart(updatedCart);
    },
    updateQuantity: (productId, quantity) => {
      const updatedCart = updateCartItemQuantity(productId, quantity);
      setCart(updatedCart);
    },
    removeFromCart: (productId) => {
      const updatedCart = removeFromCart(productId);
      setCart(updatedCart);
    },
    clearCart: () => {
      clearCart();
      setCart([]);
    },
    itemCount: cart.reduce((total, item) => total + item.quantity, 0),
    total: cart.reduce((total, item) => total + (item.price * item.quantity), 0),
    isProductInCart: (productId) => cart.some(item => item.id === productId)
  };
}
