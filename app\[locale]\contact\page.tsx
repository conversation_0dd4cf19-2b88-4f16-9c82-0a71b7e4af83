'use client';

import { useState } from 'react';
import { useParams } from 'next/navigation';
import { Locale } from '../../../lib/i18n';
import Navbar from '../../../components/Navbar';
import Footer from '../../../components/Footer';
import WhatsAppButton from '../../../components/WhatsAppButton';
import { useSiteSettings } from '../../../hooks/useSiteSettings';

export default function ContactPage() {
  const params = useParams();
  const locale = (params?.locale || 'ar') as Locale;
  const { settings, loading } = useSiteSettings();

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  // استخدام الإعدادات من لوحة التحكم أو القيم الافتراضية
  const getContactContent = () => {
    if (!settings) {
      return {
        title: locale === 'ar' ? 'تواصل معنا' : 'Contact Us',
        subtitle: locale === 'ar' ? 'نحن هنا لمساعدتك في جميع استفساراتك واحتياجاتك' : 'We are here to help you with all your inquiries and needs',
        formTitle: locale === 'ar' ? 'أرسل لنا رسالة' : 'Send us a message',
        name: locale === 'ar' ? 'الاسم الكامل' : 'Full Name',
        email: locale === 'ar' ? 'البريد الإلكتروني' : 'Email Address',
        phone: locale === 'ar' ? 'رقم الهاتف' : 'Phone Number',
        subject: locale === 'ar' ? 'الموضوع' : 'Subject',
        message: locale === 'ar' ? 'الرسالة' : 'Message',
        send: locale === 'ar' ? 'إرسال الرسالة' : 'Send Message',
        sending: locale === 'ar' ? 'جاري الإرسال...' : 'Sending...',
        successMessage: locale === 'ar' ? 'تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.' : 'Your message has been sent successfully! We will contact you soon.',
        errorMessage: locale === 'ar' ? 'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.' : 'An error occurred while sending the message. Please try again.',
        contactInfo: locale === 'ar' ? 'معلومات التواصل' : 'Contact Information',
        address: locale === 'ar' ? 'العنوان' : 'Address',
        addressText: locale === 'ar' ? 'الرياض، المملكة العربية السعودية' : 'Riyadh, Saudi Arabia',
        workingHours: locale === 'ar' ? 'ساعات العمل' : 'Working Hours',
        workingHoursText: locale === 'ar' ? 'الأحد - الخميس: 8:00 ص - 6:00 م' : 'Sunday - Thursday: 8:00 AM - 6:00 PM',
        location: locale === 'ar' ? 'موقعنا' : 'Our Location'
      };
    }

    return {
      title: locale === 'ar' ? 'تواصل معنا' : 'Contact Us',
      subtitle: locale === 'ar'
        ? settings.contactSettings?.additionalInfo?.descriptionAr || 'نحن هنا لمساعدتك في جميع استفساراتك واحتياجاتك'
        : settings.contactSettings?.additionalInfo?.description || 'We are here to help you with all your inquiries and needs',
      formTitle: locale === 'ar' ? 'أرسل لنا رسالة' : 'Send us a message',
      name: locale === 'ar' ? 'الاسم الكامل' : 'Full Name',
      email: locale === 'ar' ? 'البريد الإلكتروني' : 'Email Address',
      phone: locale === 'ar' ? 'رقم الهاتف' : 'Phone Number',
      subject: locale === 'ar' ? 'الموضوع' : 'Subject',
      message: locale === 'ar' ? 'الرسالة' : 'Message',
      send: locale === 'ar' ? 'إرسال الرسالة' : 'Send Message',
      sending: locale === 'ar' ? 'جاري الإرسال...' : 'Sending...',
      successMessage: locale === 'ar'
        ? settings.contactSettings?.contactFormSettings?.successMessageAr || 'تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.'
        : settings.contactSettings?.contactFormSettings?.successMessage || 'Your message has been sent successfully! We will contact you soon.',
      errorMessage: locale === 'ar'
        ? settings.contactSettings?.contactFormSettings?.errorMessageAr || 'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.'
        : settings.contactSettings?.contactFormSettings?.errorMessage || 'An error occurred while sending the message. Please try again.',
      contactInfo: locale === 'ar' ? 'معلومات التواصل' : 'Contact Information',
      address: locale === 'ar' ? 'العنوان' : 'Address',
      addressText: locale === 'ar' ? settings.addressAr : settings.address,
      workingHours: locale === 'ar' ? 'ساعات العمل' : 'Working Hours',
      workingHoursText: locale === 'ar'
        ? settings.contactSettings?.officeHours?.hoursTextAr || settings.workingHoursAr
        : settings.contactSettings?.officeHours?.hoursText || settings.workingHours,
      location: locale === 'ar' ? 'موقعنا' : 'Our Location',
      phoneNumber: settings.phone,
      emailAddress: settings.contactEmail
    };
  };

  const currentContent = getContactContent();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      // محاكاة إرسال النموذج
      await new Promise(resolve => setTimeout(resolve, 2000));
      setSubmitStatus('success');
      setFormData({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: ''
      });
    } catch {
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
      setTimeout(() => setSubmitStatus('idle'), 5000);
    }
  };

  // عرض حالة التحميل
  if (loading) {
    return (
      <>
        <Navbar locale={locale} />
        <main className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <i className="ri-loader-4-line text-4xl text-primary animate-spin mb-4"></i>
            <p className="text-gray-600">
              {locale === 'ar' ? 'جاري التحميل...' : 'Loading...'}
            </p>
          </div>
        </main>
        <Footer locale={locale} />
      </>
    );
  }

  return (
    <>
      <Navbar locale={locale} />
      <main>
        {/* Hero Section */}
        <section className="bg-primary py-16">
          <div className="container mx-auto px-4">
            <div className="text-center text-white">
              <h1 className="text-4xl md:text-5xl font-bold mb-4">
                {currentContent.title}
              </h1>
              <p className="text-xl text-white/90 max-w-3xl mx-auto">
                {currentContent.subtitle}
              </p>
            </div>
          </div>
        </section>

        {/* Contact Form & Info */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className={`grid grid-cols-1 ${settings?.contactSettings?.contactFormSettings?.enableContactForm ? 'lg:grid-cols-2' : 'lg:grid-cols-1'} gap-12`}>
              {/* Contact Form */}
              {settings?.contactSettings?.contactFormSettings?.enableContactForm && (
                <div className="bg-white rounded-xl p-8 shadow-lg">
                <h2 className="text-2xl font-bold text-gray-800 mb-6">
                  {currentContent.formTitle}
                </h2>
                
                {submitStatus === 'success' && (
                  <div className="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg mb-6">
                    <div className="flex items-center">
                      <i className="ri-check-circle-line text-xl mr-3"></i>
                      {currentContent.successMessage}
                    </div>
                  </div>
                )}

                {submitStatus === 'error' && (
                  <div className="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg mb-6">
                    <div className="flex items-center">
                      <i className="ri-error-warning-line text-xl mr-3"></i>
                      {currentContent.errorMessage}
                    </div>
                  </div>
                )}

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-gray-700 font-medium mb-2">
                        {currentContent.name} *
                      </label>
                      <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-gray-700 font-medium mb-2">
                        {currentContent.email} *
                      </label>
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-gray-700 font-medium mb-2">
                        {currentContent.phone}
                      </label>
                      <input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-gray-700 font-medium mb-2">
                        {currentContent.subject} *
                      </label>
                      <input
                        type="text"
                        name="subject"
                        value={formData.subject}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-gray-700 font-medium mb-2">
                      {currentContent.message} *
                    </label>
                    <textarea
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      rows={6}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
                    ></textarea>
                  </div>

                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full bg-primary text-white py-3 px-6 rounded-lg font-semibold hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                  >
                    {isSubmitting ? (
                      <>
                        <i className="ri-loader-4-line animate-spin"></i>
                        {currentContent.sending}
                      </>
                    ) : (
                      <>
                        <i className="ri-send-plane-line"></i>
                        {currentContent.send}
                      </>
                    )}
                  </button>
                </form>
              </div>
              )}

              {/* Contact Information */}
              <div className="space-y-8">
                <div className="bg-white rounded-xl p-8 shadow-lg">
                  <h2 className="text-2xl font-bold text-gray-800 mb-6">
                    {currentContent.contactInfo}
                  </h2>
                  
                  <div className="space-y-6">
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                        <i className="ri-map-pin-line text-xl text-primary"></i>
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-800 mb-1">
                          {currentContent.address}
                        </h3>
                        <p className="text-gray-600">
                          {currentContent.addressText}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                        <i className="ri-phone-line text-xl text-primary"></i>
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-800 mb-1">
                          {locale === 'ar' ? 'الهاتف' : 'Phone'}
                        </h3>
                        <p className="text-gray-600">
                          <a href={`tel:${currentContent.phoneNumber || '+966501234567'}`} className="hover:text-primary transition-colors">
                            {currentContent.phoneNumber || '+966 50 123 4567'}
                          </a>
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                        <i className="ri-mail-line text-xl text-primary"></i>
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-800 mb-1">
                          {locale === 'ar' ? 'البريد الإلكتروني' : 'Email'}
                        </h3>
                        <p className="text-gray-600">
                          <a href={`mailto:${currentContent.emailAddress || '<EMAIL>'}`} className="hover:text-primary transition-colors">
                            {currentContent.emailAddress || '<EMAIL>'}
                          </a>
                        </p>
                      </div>
                    </div>

                    {settings?.contactSettings?.officeHours?.enabled && (
                      <div className="flex items-start gap-4">
                        <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                          <i className="ri-time-line text-xl text-primary"></i>
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-800 mb-1">
                            {currentContent.workingHours}
                          </h3>
                          <p className="text-gray-600 whitespace-pre-line">
                            {currentContent.workingHoursText}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Map Section */}
                {settings?.contactSettings?.mapSettings?.showMap && (
                  <div className="bg-white rounded-xl p-8 shadow-lg">
                    <h3 className="text-xl font-bold text-gray-800 mb-4">
                      {currentContent.location}
                    </h3>

                    {settings.contactSettings.mapSettings.googleMapsUrl ? (
                      <div className="rounded-lg overflow-hidden h-64">
                        <iframe
                          src={settings.contactSettings.mapSettings.googleMapsUrl}
                          width="100%"
                          height="100%"
                          style={{ border: 0 }}
                          allowFullScreen
                          loading="lazy"
                          referrerPolicy="no-referrer-when-downgrade"
                          title={locale === 'ar' ? 'خريطة الموقع' : 'Location Map'}
                        ></iframe>
                      </div>
                    ) : (
                      <div className="bg-gray-200 rounded-lg h-64 flex items-center justify-center">
                        <div className="text-center">
                          <i className="ri-map-pin-line text-4xl text-gray-400 mb-2"></i>
                          <p className="text-gray-600">
                            {locale === 'ar' ? 'خريطة الموقع' : 'Location Map'}
                          </p>
                          {settings.contactSettings.mapSettings.latitude && settings.contactSettings.mapSettings.longitude && (
                            <p className="text-sm text-gray-500 mt-2">
                              {locale === 'ar' ? 'الإحداثيات:' : 'Coordinates:'} {settings.contactSettings.mapSettings.latitude}, {settings.contactSettings.mapSettings.longitude}
                            </p>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer locale={locale} />
      <WhatsAppButton locale={locale} />
    </>
  );
}
