'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

const AdminIndex = () => {
  const router = useRouter();

  useEffect(() => {
    // توجيه تلقائي إلى صفحة تسجيل الدخول
    router.replace('/admin/login');
  }, [router]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
    </div>
  );
};

export default AdminIndex;
