import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import crypto from 'crypto';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({
        success: false,
        message: 'No file provided',
        messageAr: 'لم يتم توفير ملف'
      }, { status: 400 });
    }

    // التحقق من نوع الملف
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({
        success: false,
        message: 'Invalid file type. Only images are allowed.',
        messageAr: 'نوع ملف غير صحيح. الصور فقط مسموحة.'
      }, { status: 400 });
    }

    // التحقق من حجم الملف (5MB max)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return NextResponse.json({
        success: false,
        message: 'File too large. Maximum size is 5MB.',
        messageAr: 'الملف كبير جداً. الحد الأقصى 5 ميجابايت.'
      }, { status: 400 });
    }

    // إنشاء مجلد الرفع إذا لم يكن موجوداً
    const uploadDir = path.join(process.cwd(), 'public', 'uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    // إنشاء اسم ملف فريد وآمن
    const timestamp = Date.now();
    const randomString = crypto.randomBytes(8).toString('hex');
    const fileExtension = path.extname(file.name);
    const fileName = `${timestamp}_${randomString}${fileExtension}`;
    const filePath = path.join(uploadDir, fileName);

    // حفظ الملف
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    fs.writeFileSync(filePath, buffer);

    // إرجاع رابط الملف
    const fileUrl = `/uploads/${fileName}`;

    console.log(`✅ تم رفع الملف بنجاح: ${fileName}`);

    return NextResponse.json({
      success: true,
      message: 'File uploaded successfully',
      messageAr: 'تم رفع الملف بنجاح',
      data: {
        url: fileUrl,
        filename: fileName,
        originalName: file.name,
        size: file.size,
        type: file.type
      }
    });

  } catch (error: unknown) {
    console.error('❌ خطأ في رفع الملف:', error);
    return NextResponse.json({
      success: false,
      message: 'Upload failed',
      messageAr: 'فشل في رفع الملف',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}