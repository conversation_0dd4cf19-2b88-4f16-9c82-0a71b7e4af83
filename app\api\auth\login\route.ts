import { NextRequest, NextResponse } from 'next/server';
import { generateToken } from '../../../../lib/auth';
import { validateAdminLogin } from '../../../../lib/secure-storage';
import { serialize } from 'cookie';
import {
  checkRateLimit,
  RATE_LIMIT_CONFIGS,
  getClientIP,
  isIPBlocked,
  logSuspiciousActivity
} from '../../../../lib/rate-limiter';

// POST - تسجيل الدخول
export async function POST(request: NextRequest) {
  try {
    const clientIP = getClientIP(request);

    // فحص ما إذا كان IP محظوراً
    if (isIPBlocked(clientIP)) {
      logSuspiciousActivity(
        clientIP,
        '/api/auth/login',
        'Blocked IP attempting login',
        request.headers.get('user-agent') || ''
      );

      return NextResponse.json({
        success: false,
        message: 'Access denied. IP blocked due to suspicious activity.',
        messageAr: 'تم رفض الوصول. تم حظر عنوان IP بسبب نشاط مشبوه.'
      }, { status: 403 });
    }

    // فحص معدل الطلبات
    const rateLimitResult = checkRateLimit(request, 'login', RATE_LIMIT_CONFIGS.LOGIN);

    if (!rateLimitResult.allowed) {
      logSuspiciousActivity(
        clientIP,
        '/api/auth/login',
        'Rate limit exceeded - possible brute force attack',
        request.headers.get('user-agent') || ''
      );

      return NextResponse.json({
        success: false,
        message: 'Too many login attempts. Please try again later.',
        messageAr: 'محاولات تسجيل دخول كثيرة جداً. يرجى المحاولة لاحقاً.',
        retryAfter: rateLimitResult.retryAfter
      }, { status: 429 });
    }
    
    const body = await request.json();
    const { username, password } = body;
    
    // التحقق من وجود البيانات المطلوبة
    if (!username || !password) {
      return NextResponse.json({
        success: false,
        message: 'Username and password are required',
        messageAr: 'اسم المستخدم وكلمة المرور مطلوبان'
      }, { status: 400 });
    }

    // التحقق من صحة البيانات
    if (typeof username !== 'string' || typeof password !== 'string') {
      return NextResponse.json({
        success: false,
        message: 'Invalid data format',
        messageAr: 'تنسيق البيانات غير صحيح'
      }, { status: 400 });
    }

    // التحقق من طول البيانات
    if (username.length > 50 || password.length > 100) {
      return NextResponse.json({
        success: false,
        message: 'Invalid data length',
        messageAr: 'طول البيانات غير صحيح'
      }, { status: 400 });
    }
    
    // محاولة تسجيل الدخول
    const user = await validateAdminLogin(username.trim(), password);

    if (!user) {
      logSuspiciousActivity(
        clientIP,
        '/api/auth/login',
        'Failed login attempt - invalid credentials',
        request.headers.get('user-agent') || ''
      );

      return NextResponse.json({
        success: false,
        message: 'Invalid username or password',
        messageAr: 'اسم المستخدم أو كلمة المرور غير صحيحة'
      }, { status: 401 });
    }

    // نجح تسجيل الدخول - تسجيل النشاط الإيجابي
    console.log(`✅ Successful login for user: ${user.username} from IP: ${clientIP}`);
    
    // إنشاء JWT token
    const token = generateToken(user);
    
    // إعداد cookie آمن
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict' as const,
      maxAge: 24 * 60 * 60, // 24 ساعة
      path: '/'
    };
    
    const cookie = serialize('authToken', token, cookieOptions);
    
    // إنشاء الاستجابة
    const response = NextResponse.json({
      success: true,
      message: 'Login successful',
      messageAr: 'تم تسجيل الدخول بنجاح',
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role
      },
      token // إرسال التوكن أيضاً للاستخدام في الواجهة الأمامية
    });

    // إضافة cookie إلى الاستجابة
    response.headers.set('Set-Cookie', cookie);
    
    return response;
    
  } catch (error) {
    console.error('Login API error:', error);

    const clientIP = getClientIP(request);
    logSuspiciousActivity(
      clientIP,
      '/api/auth/login',
      'Server error during login attempt',
      request.headers.get('user-agent') || ''
    );

    return NextResponse.json({
      success: false,
      message: 'Internal server error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}
