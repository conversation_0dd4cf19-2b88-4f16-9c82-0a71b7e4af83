const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');

// إعدادات قاعدة البيانات
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'droobhajer_db',
  charset: 'utf8mb4'
};

async function checkAdminUsers() {
  let connection = null;
  
  try {
    console.log('🔄 الاتصال بقاعدة البيانات...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
    
    // التحقق من وجود جدول admins
    console.log('\n📋 التحقق من جدول admins...');
    const [tables] = await connection.execute(
      "SHOW TABLES LIKE 'admins'"
    );
    
    if (tables.length === 0) {
      console.log('❌ جدول admins غير موجود!');
      console.log('💡 يرجى إنشاء الجدول أولاً باستخدام:');
      console.log(`
CREATE TABLE \`admins\` (
  \`id\` int NOT NULL AUTO_INCREMENT,
  \`username\` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  \`email\` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  \`password_hash\` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  \`is_active\` tinyint(1) DEFAULT '1',
  \`last_login\` timestamp NULL DEFAULT NULL,
  \`created_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  \`updated_at\` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  \`deleted_at\` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (\`id\`),
  UNIQUE KEY \`username\` (\`username\`),
  UNIQUE KEY \`email\` (\`email\`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
      `);
      return;
    }
    
    console.log('✅ جدول admins موجود');
    
    // عرض جميع المستخدمين
    console.log('\n👥 المستخدمين الموجودين:');
    const [users] = await connection.execute(
      'SELECT id, username, email, is_active, created_at, last_login FROM admins WHERE deleted_at IS NULL'
    );
    
    if (users.length === 0) {
      console.log('❌ لا توجد مستخدمين في قاعدة البيانات');
      console.log('💡 سيتم إنشاء مستخدم افتراضي...');
      await createDefaultAdmin(connection);
    } else {
      console.table(users);
      
      // اختبار كلمة المرور للمستخدم admin
      const adminUser = users.find(u => u.username === 'admin');
      if (adminUser) {
        console.log('\n🔐 اختبار كلمة المرور للمستخدم admin...');
        const [adminData] = await connection.execute(
          'SELECT password_hash FROM admins WHERE username = ?',
          ['admin']
        );
        
        if (adminData.length > 0) {
          const isValid = await bcrypt.compare('admin123', adminData[0].password_hash);
          console.log(`كلمة المرور 'admin123': ${isValid ? '✅ صحيحة' : '❌ خاطئة'}`);
          
          if (!isValid) {
            console.log('💡 سيتم إعادة تعيين كلمة المرور...');
            await resetAdminPassword(connection);
          }
        }
      }
    }
    
  } catch (error) {
    console.error('❌ خطأ:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 تأكد من أن خادم MySQL يعمل');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('💡 تحقق من بيانات الاتصال بقاعدة البيانات');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('💡 تأكد من وجود قاعدة البيانات droobhajer_db');
    }
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

async function createDefaultAdmin(connection) {
  try {
    const defaultPassword = 'admin123';
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(defaultPassword, saltRounds);
    
    await connection.execute(
      `INSERT INTO admins (username, email, password_hash, is_active, created_at, updated_at) 
       VALUES (?, ?, ?, 1, NOW(), NOW())`,
      ['admin', '<EMAIL>', passwordHash]
    );
    
    console.log('✅ تم إنشاء المستخدم الافتراضي:');
    console.log('   اسم المستخدم: admin');
    console.log('   كلمة المرور: admin123');
    console.log('   البريد الإلكتروني: <EMAIL>');
    
  } catch (error) {
    console.error('❌ خطأ في إنشاء المستخدم الافتراضي:', error.message);
  }
}

async function resetAdminPassword(connection) {
  try {
    const defaultPassword = 'admin123';
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(defaultPassword, saltRounds);
    
    await connection.execute(
      'UPDATE admins SET password_hash = ?, updated_at = NOW() WHERE username = ?',
      [passwordHash, 'admin']
    );
    
    console.log('✅ تم إعادة تعيين كلمة المرور للمستخدم admin');
    console.log('   كلمة المرور الجديدة: admin123');
    
  } catch (error) {
    console.error('❌ خطأ في إعادة تعيين كلمة المرور:', error.message);
  }
}

// تشغيل الفحص
checkAdminUsers();
