const CryptoJS = require('crypto-js');
const bcrypt = require('bcryptjs');
const fs = require('fs');
const path = require('path');

// إعدادات التشفير
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'droobhajer-encryption-key-2024-very-secure-for-development-only-change-in-production';
const SECURE_DATA_PATH = path.join(process.cwd(), 'data', 'secure-admin.enc');

// تشفير البيانات
function encryptData(data) {
  try {
    const jsonString = JSON.stringify(data);
    const encrypted = CryptoJS.AES.encrypt(jsonString, ENCRYPTION_KEY).toString();
    return encrypted;
  } catch (error) {
    console.error('Error encrypting data:', error);
    throw new Error('Failed to encrypt data');
  }
}

// إنشاء مجلد البيانات
function ensureDataDirectory() {
  const dataDir = path.dirname(SECURE_DATA_PATH);
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
}

// إنشاء مستخدم إداري آمن
async function createDefaultAdminUser() {
  try {
    console.log('🔐 إنشاء مستخدم إداري آمن...');
    
    const defaultPassword = process.env.DEFAULT_ADMIN_PASSWORD || 'DroobHajer@2024!Secure';
    
    // تشفير كلمة المرور
    const salt = await bcrypt.genSalt(12);
    const passwordHash = await bcrypt.hash(defaultPassword, salt);
    
    const adminUser = {
      id: '1',
      username: 'admin',
      email: process.env.ADMIN_EMAIL || '<EMAIL>',
      passwordHash,
      role: 'admin',
      lastLogin: new Date(),
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    ensureDataDirectory();
    
    // تشفير وحفظ البيانات
    const encryptedData = encryptData(adminUser);
    fs.writeFileSync(SECURE_DATA_PATH, encryptedData, 'utf8');
    
    console.log('✅ تم إنشاء المستخدم الإداري بنجاح!');
    console.log('📧 البريد الإلكتروني:', adminUser.email);
    console.log('👤 اسم المستخدم: admin');
    console.log('🔑 كلمة المرور:', defaultPassword);
    console.log('⚠️  يرجى تغيير كلمة المرور بعد أول تسجيل دخول!');
    
    return adminUser;
  } catch (error) {
    console.error('❌ خطأ في إنشاء المستخدم الإداري:', error);
    throw error;
  }
}

// تشغيل الإنشاء
if (require.main === module) {
  createDefaultAdminUser()
    .then(() => {
      console.log('\n🎉 تم الانتهاء من إنشاء المستخدم الإداري!');
    })
    .catch((error) => {
      console.error('\n💥 فشل في إنشاء المستخدم الإداري:', error.message);
      process.exit(1);
    });
}

module.exports = { createDefaultAdminUser };
