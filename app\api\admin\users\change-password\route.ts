import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { executeQuery, executeQuerySingle } from '../../../../../lib/database-config';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

interface AdminUser {
  id: number;
  username: string;
  email: string;
  password_hash: string;
  is_active: boolean;
}

// التحقق من صحة JWT token
function verifyToken(token: string) {
  try {
    return jwt.verify(token, JWT_SECRET) as any;
  } catch (error) {
    return null;
  }
}

// استخراج token من الطلب
function extractToken(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  const tokenFromCookie = request.cookies.get('authToken')?.value;
  if (tokenFromCookie) {
    return tokenFromCookie;
  }

  return null;
}

// PUT - تغيير كلمة المرور
export async function PUT(request: NextRequest) {
  try {
    // التحقق من المصادقة
    const token = extractToken(request);
    if (!token) {
      return NextResponse.json({
        message: 'غير مصرح لك بالوصول',
        success: false
      }, { status: 401 });
    }

    const decoded = verifyToken(token);
    if (!decoded) {
      return NextResponse.json({
        message: 'رمز المصادقة غير صحيح',
        success: false
      }, { status: 401 });
    }

    const body = await request.json();
    const { userId, currentPassword, newPassword } = body;

    // التحقق من وجود البيانات المطلوبة
    if (!userId || !currentPassword || !newPassword) {
      return NextResponse.json({
        message: 'جميع الحقول مطلوبة',
        success: false
      }, { status: 400 });
    }

    // التحقق من قوة كلمة المرور الجديدة
    if (newPassword.length < 6) {
      return NextResponse.json({
        message: 'كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل',
        success: false
      }, { status: 400 });
    }

    // الحصول على بيانات المستخدم
    const user = await executeQuerySingle<AdminUser>(
      'SELECT id, username, email, password_hash, is_active FROM admins WHERE id = ? AND deleted_at IS NULL',
      [userId]
    );

    if (!user) {
      return NextResponse.json({
        message: 'المستخدم غير موجود',
        success: false
      }, { status: 404 });
    }

    // التحقق من كلمة المرور الحالية
    const isValidCurrentPassword = await bcrypt.compare(currentPassword, user.password_hash);

    if (!isValidCurrentPassword) {
      return NextResponse.json({
        message: 'كلمة المرور الحالية غير صحيحة',
        success: false
      }, { status: 400 });
    }

    // التحقق من أن كلمة المرور الجديدة مختلفة عن الحالية
    const isSamePassword = await bcrypt.compare(newPassword, user.password_hash);

    if (isSamePassword) {
      return NextResponse.json({
        message: 'كلمة المرور الجديدة يجب أن تكون مختلفة عن الحالية',
        success: false
      }, { status: 400 });
    }

    // تشفير كلمة المرور الجديدة
    const saltRounds = 12;
    const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);

    // تحديث كلمة المرور في قاعدة البيانات
    await executeQuery(
      'UPDATE admins SET password_hash = ?, updated_at = NOW() WHERE id = ?',
      [newPasswordHash, userId]
    );

    return NextResponse.json({
      success: true,
      message: 'تم تغيير كلمة المرور بنجاح'
    });

  } catch (error) {
    console.error('Change password error:', error);
    return NextResponse.json({
      message: 'حدث خطأ في الخادم',
      success: false
    }, { status: 500 });
  }
}
