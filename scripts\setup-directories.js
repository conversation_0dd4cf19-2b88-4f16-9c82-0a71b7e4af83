// سكريبت إنشاء المجلدات المطلوبة للنظام
const fs = require('fs');
const path = require('path');

function createDirectory(dirPath, description) {
  try {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
      console.log(`✅ تم إنشاء ${description}: ${dirPath}`);
    } else {
      console.log(`📁 ${description} موجود بالفعل: ${dirPath}`);
    }

    // التحقق من صلاحيات الكتابة
    fs.accessSync(dirPath, fs.constants.W_OK);
    console.log(`✅ صلاحيات الكتابة متاحة في: ${dirPath}`);
    
    return true;
  } catch (error) {
    console.error(`❌ خطأ في إنشاء ${description}:`, error.message);
    return false;
  }
}

function setupDirectories() {
  console.log('🔄 بدء إعداد المجلدات المطلوبة...\n');

  const projectRoot = process.cwd();
  const directories = [
    {
      path: path.join(projectRoot, 'public'),
      description: 'مجلد public'
    },
    {
      path: path.join(projectRoot, 'public', 'uploads'),
      description: 'مجلد uploads'
    },
    {
      path: path.join(projectRoot, 'public', 'uploads', 'excel'),
      description: 'مجلد Excel files'
    },
    {
      path: path.join(projectRoot, 'public', 'uploads', 'images'),
      description: 'مجلد الصور'
    },
    {
      path: path.join(projectRoot, 'secure-uploads'),
      description: 'مجلد secure uploads'
    },
    {
      path: path.join(projectRoot, 'temp-uploads'),
      description: 'مجلد temp uploads'
    }
  ];

  let allSuccess = true;

  directories.forEach(dir => {
    const success = createDirectory(dir.path, dir.description);
    if (!success) {
      allSuccess = false;
    }
  });

  console.log('\n' + '='.repeat(50));
  
  if (allSuccess) {
    console.log('🎉 تم إعداد جميع المجلدات بنجاح!');
    console.log('✅ النظام جاهز لاستقبال طلبات التسعير');
  } else {
    console.log('⚠️  تم إعداد بعض المجلدات مع وجود مشاكل');
    console.log('🔧 يرجى التحقق من الأخطاء أعلاه وإصلاحها');
  }

  // إنشاء ملف .gitkeep في المجلدات الفارغة
  const gitkeepDirs = [
    path.join(projectRoot, 'public', 'uploads', 'excel'),
    path.join(projectRoot, 'public', 'uploads', 'images'),
    path.join(projectRoot, 'temp-uploads')
  ];

  console.log('\n📝 إنشاء ملفات .gitkeep...');
  gitkeepDirs.forEach(dir => {
    const gitkeepPath = path.join(dir, '.gitkeep');
    try {
      if (!fs.existsSync(gitkeepPath)) {
        fs.writeFileSync(gitkeepPath, '# This file keeps the directory in git\n');
        console.log(`✅ تم إنشاء .gitkeep في: ${dir}`);
      }
    } catch (error) {
      console.log(`⚠️  لم يتم إنشاء .gitkeep في: ${dir}`);
    }
  });

  console.log('\n📋 ملخص المجلدات:');
  directories.forEach(dir => {
    const exists = fs.existsSync(dir.path);
    const status = exists ? '✅' : '❌';
    console.log(`${status} ${dir.description}: ${dir.path}`);
  });
}

// تشغيل السكريبت
setupDirectories();
