import { NextRequest, NextResponse } from 'next/server';
import { readSecureFile, getSecureFileInfo } from '@/lib/secure-upload';
import path from 'path';

// Cache control headers
const CACHE_MAX_AGE = 60 * 60 * 24 * 7; // أسبوع واحد

// MIME types للملفات
const MIME_TYPES: { [key: string]: string } = {
  '.jpg': 'image/jpeg',
  '.jpeg': 'image/jpeg',
  '.png': 'image/png',
  '.gif': 'image/gif',
  '.webp': 'image/webp'
};

export async function GET(
  request: NextRequest,
  { params }: { params: { filename: string } }
) {
  try {
    const { filename } = params;

    // التحقق من صحة اسم الملف
    if (!filename || typeof filename !== 'string') {
      return NextResponse.json({
        success: false,
        message: 'Invalid filename'
      }, { status: 400 });
    }

    // منع Path Traversal attacks
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      return NextResponse.json({
        success: false,
        message: 'Invalid filename format'
      }, { status: 400 });
    }

    // التحقق من امتداد الملف
    const ext = path.extname(filename).toLowerCase();
    if (!MIME_TYPES[ext]) {
      return NextResponse.json({
        success: false,
        message: 'Unsupported file type'
      }, { status: 400 });
    }

    // قراءة الملف الآمن
    const fileBuffer = readSecureFile(filename);
    if (!fileBuffer) {
      return NextResponse.json({
        success: false,
        message: 'File not found'
      }, { status: 404 });
    }

    // الحصول على معلومات الملف
    const fileInfo = getSecureFileInfo(filename);
    if (!fileInfo) {
      return NextResponse.json({
        success: false,
        message: 'File info not found'
      }, { status: 404 });
    }

    // إعداد headers الاستجابة
    const mimeType = MIME_TYPES[ext];

    // التحقق من If-None-Match للـ caching
    const ifNoneMatch = request.headers.get('if-none-match');
    const etag = `"${filename}-${fileInfo.mtime.getTime()}"`;

    if (ifNoneMatch === etag) {
      return new NextResponse(null, { status: 304 });
    }

    // إنشاء الاستجابة مع الملف
    const response = new NextResponse(fileBuffer);

    // إعداد headers
    response.headers.set('Content-Type', mimeType);
    response.headers.set('Content-Length', fileBuffer.length.toString());
    response.headers.set('Cache-Control', `public, max-age=${CACHE_MAX_AGE}`);
    response.headers.set('ETag', etag);

    // إضافة headers أمان
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('Content-Security-Policy', "default-src 'none'; img-src 'self'; style-src 'unsafe-inline'");

    return response;

  } catch (error) {
    console.error('File serving error:', error);

    return NextResponse.json({
      success: false,
      message: 'Internal server error'
    }, { status: 500 });
  }
}
