import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    // التحقق من صحة البيانات
    if (!email || !email.includes('@')) {
      return NextResponse.json({
        success: false,
        message: 'يرجى إدخال إيميل صحيح'
      }, { status: 400 });
    }

      // إنشاء مجلد البيانات إذا لم يكن موجوداً
      const dataDir = path.join(process.cwd(), 'src', 'data');
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }

      // حفظ إعدادات الشركة
      const companySettings = {
        email,
        updatedAt: new Date().toISOString()
      };

      const settingsPath = path.join(dataDir, 'company-settings.json');
    fs.writeFileSync(settingsPath, JSON.stringify(companySettings, null, 2));

    return NextResponse.json({
      success: true,
      message: 'تم حفظ إعدادات الشركة بنجاح',
      settings: companySettings
    });

  } catch (error) {
    console.error('Error saving company settings:', error);
    return NextResponse.json({
      success: false,
      message: 'حدث خطأ أثناء حفظ الإعدادات'
    }, { status: 500 });
  }
}

export async function GET(_request: NextRequest) {
  try {
    const settingsPath = path.join(process.cwd(), 'src', 'data', 'company-settings.json');

    if (!fs.existsSync(settingsPath)) {
      return NextResponse.json({
        success: true,
        settings: { email: '' }
      });
    }

    const settingsContent = fs.readFileSync(settingsPath, 'utf-8');
    const settings = JSON.parse(settingsContent);

    return NextResponse.json({
      success: true,
      settings
    });

  } catch (error) {
    console.error('Error loading company settings:', error);
    return NextResponse.json({
      success: false,
      message: 'حدث خطأ أثناء تحميل الإعدادات'
    }, { status: 500 });
  }
}
