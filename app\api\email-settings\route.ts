import { NextApiRequest, NextApiResponse } from 'next';
import fs from 'fs';
import path from 'path';

interface EmailSettings {
  smtpHost: string;
  smtpPort: number;
  smtpSecure: boolean;
  smtpUser: string;
  smtpPass: string;
  fromName: string;
  fromNameAr: string;
  enabled: boolean;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'POST') {
    try {
      const { 
        smtpHost, 
        smtpPort, 
        smtpSecure, 
        smtpUser, 
        smtpPass, 
        fromName, 
        fromNameAr, 
        enabled 
      } = req.body;

      // التحقق من صحة البيانات الأساسية
      if (!smtpHost || !smtpUser || !smtpPass) {
        return res.status(400).json({
          success: false,
          message: 'يرجى إدخال جميع البيانات المطلوبة (خادم SMTP، اسم المستخدم، كلمة المرور)'
        });
      }

      // التحقق من صحة الإيميل
      if (!smtpUser.includes('@')) {
        return res.status(400).json({
          success: false,
          message: 'يرجى إدخال إيميل صحيح'
        });
      }

      // إنشاء مجلد البيانات إذا لم يكن موجوداً
      const dataDir = path.join(process.cwd(), 'src', 'data');
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }

      // إعداد البيانات
      const emailSettings: EmailSettings = {
        smtpHost: smtpHost || 'smtp.gmail.com',
        smtpPort: parseInt(smtpPort) || 587,
        smtpSecure: smtpSecure || false,
        smtpUser,
        smtpPass,
        fromName: fromName || 'Droob Hajer',
        fromNameAr: fromNameAr || 'دروب هاجر',
        enabled: enabled !== false
      };

      // حفظ إعدادات الإيميل
      const settingsPath = path.join(dataDir, 'email-settings.json');
      const settingsWithTimestamp = {
        ...emailSettings,
        updatedAt: new Date().toISOString()
      };

      fs.writeFileSync(settingsPath, JSON.stringify(settingsWithTimestamp, null, 2));

      // إرجاع البيانات بدون كلمة المرور للأمان
      const responseSettings = { ...emailSettings };
      delete (responseSettings as any).smtpPass;

      res.status(200).json({
        success: true,
        message: 'تم حفظ إعدادات الإيميل بنجاح',
        settings: responseSettings
      });

    } catch (error) {
      console.error('Error saving email settings:', error);
      res.status(500).json({
        success: false,
        message: 'حدث خطأ أثناء حفظ إعدادات الإيميل'
      });
    }
  } else if (req.method === 'GET') {
    try {
      const settingsPath = path.join(process.cwd(), 'src', 'data', 'email-settings.json');
      
      if (!fs.existsSync(settingsPath)) {
        return res.status(200).json({
          success: true,
          settings: {
            smtpHost: 'smtp.gmail.com',
            smtpPort: 587,
            smtpSecure: false,
            smtpUser: '',
            smtpPass: '',
            fromName: 'Droob Hajer',
            fromNameAr: 'دروب هاجر',
            enabled: false
          }
        });
      }

      const settingsContent = fs.readFileSync(settingsPath, 'utf-8');
      const settings = JSON.parse(settingsContent);

      // إخفاء كلمة المرور للأمان
      const responseSettings = { ...settings };
      delete responseSettings.smtpPass;

      res.status(200).json({
        success: true,
        settings: responseSettings
      });

    } catch (error) {
      console.error('Error loading email settings:', error);
      res.status(500).json({
        success: false,
        message: 'حدث خطأ أثناء تحميل إعدادات الإيميل'
      });
    }
  } else if (req.method === 'DELETE') {
    try {
      const settingsPath = path.join(process.cwd(), 'src', 'data', 'email-settings.json');
      
      if (fs.existsSync(settingsPath)) {
        fs.unlinkSync(settingsPath);
      }

      res.status(200).json({
        success: true,
        message: 'تم حذف إعدادات الإيميل بنجاح'
      });

    } catch (error) {
      console.error('Error deleting email settings:', error);
      res.status(500).json({
        success: false,
        message: 'حدث خطأ أثناء حذف إعدادات الإيميل'
      });
    }
  } else {
    res.setHeader('Allow', ['GET', 'POST', 'DELETE']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
