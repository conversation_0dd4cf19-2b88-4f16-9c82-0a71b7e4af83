import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  try {
    const { path: imagePath } = params;

    if (!imagePath || !Array.isArray(imagePath)) {
      return NextResponse.json({ message: 'Invalid path' }, { status: 400 });
    }

    // بناء مسار الملف
    const fileName = imagePath.join('/');
    const filePath = path.join(process.cwd(), 'public', 'uploads', fileName);

    // التحقق من وجود الملف
    if (!fs.existsSync(filePath)) {
      return NextResponse.json({ message: 'Image not found' }, { status: 404 });
    }

    // قراءة الملف
    const fileBuffer = fs.readFileSync(filePath);

    // تحديد نوع المحتوى بناءً على امتداد الملف
    const ext = path.extname(fileName).toLowerCase();
    let contentType = 'image/jpeg'; // افتراضي

    switch (ext) {
      case '.png':
        contentType = 'image/png';
        break;
      case '.jpg':
      case '.jpeg':
        contentType = 'image/jpeg';
        break;
      case '.gif':
        contentType = 'image/gif';
        break;
      case '.webp':
        contentType = 'image/webp';
        break;
      case '.svg':
        contentType = 'image/svg+xml';
        break;
    }

    // إنشاء الاستجابة مع الملف
    const response = new NextResponse(fileBuffer);

    // تعيين headers للتخزين المؤقت
    response.headers.set('Content-Type', contentType);
    response.headers.set('Cache-Control', 'public, max-age=31536000, immutable');
    response.headers.set('Content-Length', fileBuffer.length.toString());

    return response;

  } catch (error) {
    console.error('Error serving image:', error);
    return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
  }
}
