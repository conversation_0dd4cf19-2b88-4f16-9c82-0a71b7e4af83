import { NextRequest, NextResponse } from 'next/server';
import {
  getProductsWithDetails,
  addProductWithDetails,
  updateProductWithDetails,
  deleteProductWithDetails,
  getProductWithDetails,
  getProductsByCategory,
  getProductsBySubcategory
} from '@/lib/mysql-database';
import { requireAdminAuth } from '@/lib/auth';
import { v4 as uuidv4 } from 'uuid';

// GET - جلب المنتجات
export async function GET(request: NextRequest) {
  try {
    // TODO: إعادة تفعيل المصادقة لاحقاً
    // const user = requireAdminAuth(request);
    // if (!user) {
    //   return NextResponse.json({
    //     success: false,
    //     message: 'Authentication required',
    //     messageAr: 'المصادقة مطلوبة'
    //   }, { status: 401 });
    // }

    const { searchParams } = new URL(request.url);
    const categoryId = searchParams.get('categoryId');
    const subcategoryId = searchParams.get('subcategoryId');
    const id = searchParams.get('id');

    // إذا تم تمرير ID محدد، جلب منتج واحد مع التفاصيل
    if (id && typeof id === 'string') {
      const product = await getProductWithDetails(id);
      if (!product) {
        return NextResponse.json({
          success: false,
          message: 'Product not found',
          messageAr: 'المنتج غير موجود'
        }, { status: 404 });
      }
      return NextResponse.json({ success: true, data: product });
    }

    // جلب المنتجات حسب الفئة أو الفئة الفرعية أو جميع المنتجات
    let products;
    if (subcategoryId && typeof subcategoryId === 'string') {
      products = await getProductsBySubcategory(subcategoryId);
    } else if (categoryId && typeof categoryId === 'string') {
      products = await getProductsByCategory(categoryId);
    } else {
      products = await getProductsWithDetails();
    }

    return NextResponse.json({ success: true, data: products });
  } catch (error) {
    console.error('Admin Products GET API Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}

// POST - إضافة منتج جديد
export async function POST(request: NextRequest) {
  try {
    // TODO: إعادة تفعيل المصادقة لاحقاً
    // const user = requireAdminAuth(request);
    // if (!user) {
    //   return NextResponse.json({
    //     success: false,
    //     message: 'Authentication required',
    //     messageAr: 'المصادقة مطلوبة'
    //   }, { status: 401 });
    // }

    const body = await request.json();
    const {
      title,
      titleAr,
      description,
      descriptionAr,
      images,
      price,
      originalPrice,
      available,
      categoryId: catId,
      subcategoryId: subCatId,
      features,
      featuresAr,
      specifications,
      isActive,
      isFeatured
    } = body;

    if (!title || !titleAr || !description || !descriptionAr || !catId || !subCatId || price === undefined) {
      return NextResponse.json({
        success: false,
        message: 'Required fields are missing',
        messageAr: 'الحقول المطلوبة مفقودة'
      }, { status: 400 });
    }

    // تحضير بيانات المنتج
    const productId = uuidv4();
    const productData = {
      product: {
        id: productId,
        title,
        title_ar: titleAr,
        description,
        description_ar: descriptionAr,
        price: parseFloat(price),
        original_price: originalPrice ? parseFloat(originalPrice) : undefined,
        is_available: available !== undefined ? available : true,
        category_id: catId,
        subcategory_id: subCatId,
        is_active: isActive !== undefined ? isActive : true,
        is_featured: isFeatured !== undefined ? isFeatured : false
      },
      images: images || [],
      features: [] as { text: string; textAr: string }[],
      specifications: [] as { key: string; keyAr: string; value: string; valueAr: string }[]
    };

    // معالجة الميزات
    if (features && featuresAr && Array.isArray(features) && Array.isArray(featuresAr)) {
      for (let i = 0; i < Math.min(features.length, featuresAr.length); i++) {
        if (features[i] && featuresAr[i] && features[i].trim() && featuresAr[i].trim()) {
          productData.features.push({
            text: features[i].trim(),
            textAr: featuresAr[i].trim()
          });
        }
      }
    }

    // معالجة المواصفات
    if (specifications && Array.isArray(specifications)) {
      for (const spec of specifications) {
        if (spec.nameEn && spec.nameAr && spec.valueEn && spec.valueAr &&
            spec.nameEn.trim() && spec.nameAr.trim() && spec.valueEn.trim() && spec.valueAr.trim()) {
          productData.specifications.push({
            key: spec.nameEn.trim(),
            keyAr: spec.nameAr.trim(),
            value: spec.valueEn.trim(),
            valueAr: spec.valueAr.trim()
          });
        }
      }
    }

    const newProduct = await addProductWithDetails(productData);
    return NextResponse.json({
      success: true,
      data: newProduct,
      message: 'Product created successfully',
      messageAr: 'تم إنشاء المنتج بنجاح'
    }, { status: 201 });
  } catch (error) {
    console.error('Admin Products POST API Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}

// PUT - تحديث منتج
export async function PUT(request: NextRequest) {
  try {
    // TODO: إعادة تفعيل المصادقة لاحقاً
    // const user = requireAdminAuth(request);
    // if (!user) {
    //   return NextResponse.json({
    //     success: false,
    //     message: 'Authentication required',
    //     messageAr: 'المصادقة مطلوبة'
    //   }, { status: 401 });
    // }

    const { searchParams } = new URL(request.url);
    const updateId = searchParams.get('id');

    if (!updateId || typeof updateId !== 'string') {
      return NextResponse.json({
        success: false,
        message: 'Product ID is required',
        messageAr: 'معرف المنتج مطلوب'
      }, { status: 400 });
    }

    const existingProduct = await getProductWithDetails(updateId);
    if (!existingProduct) {
      return NextResponse.json({
        success: false,
        message: 'Product not found',
        messageAr: 'المنتج غير موجود'
      }, { status: 404 });
    }

    const body = await request.json();
    const {
      title: updateTitle,
      titleAr: updateTitleAr,
      description: updateDescription,
      descriptionAr: updateDescriptionAr,
      images: updateImages,
      price: updatePrice,
      originalPrice: updateOriginalPrice,
      available: updateAvailable,
      categoryId: updateCatId,
      subcategoryId: updateSubCatId,
      features: updateFeatures,
      featuresAr: updateFeaturesAr,
      specifications: updateSpecifications,
      isActive: updateIsActive,
      isFeatured: updateIsFeatured
    } = body;

    // تحضير بيانات التحديث
    const updateData: any = {};

    // تحديث بيانات المنتج الأساسية
    if (updateTitle !== undefined || updateTitleAr !== undefined || updateDescription !== undefined ||
        updateDescriptionAr !== undefined || updatePrice !== undefined || updateOriginalPrice !== undefined ||
        updateAvailable !== undefined || updateCatId !== undefined || updateSubCatId !== undefined ||
        updateIsActive !== undefined || updateIsFeatured !== undefined) {

      updateData.product = {};

      if (updateTitle !== undefined) updateData.product.title = updateTitle;
      if (updateTitleAr !== undefined) updateData.product.title_ar = updateTitleAr;
      if (updateDescription !== undefined) updateData.product.description = updateDescription;
      if (updateDescriptionAr !== undefined) updateData.product.description_ar = updateDescriptionAr;
      if (updatePrice !== undefined) updateData.product.price = parseFloat(updatePrice);
      if (updateOriginalPrice !== undefined) updateData.product.original_price = updateOriginalPrice ? parseFloat(updateOriginalPrice) : undefined;
      if (updateAvailable !== undefined) updateData.product.is_available = updateAvailable;
      if (updateCatId !== undefined) updateData.product.category_id = updateCatId;
      if (updateSubCatId !== undefined) updateData.product.subcategory_id = updateSubCatId;
      if (updateIsActive !== undefined) updateData.product.is_active = updateIsActive;
      if (updateIsFeatured !== undefined) updateData.product.is_featured = updateIsFeatured;
    }

    // تحديث الصور
    if (updateImages !== undefined && Array.isArray(updateImages)) {
      updateData.images = updateImages.filter(img => img && img.trim());
    }

    // تحديث المميزات
    if (updateFeatures !== undefined && updateFeaturesAr !== undefined &&
        Array.isArray(updateFeatures) && Array.isArray(updateFeaturesAr)) {
      updateData.features = [];
      for (let i = 0; i < Math.min(updateFeatures.length, updateFeaturesAr.length); i++) {
        if (updateFeatures[i] && updateFeaturesAr[i] && updateFeatures[i].trim() && updateFeaturesAr[i].trim()) {
          updateData.features.push({
            text: updateFeatures[i].trim(),
            textAr: updateFeaturesAr[i].trim()
          });
        }
      }
    }

    // تحديث المواصفات
    if (updateSpecifications !== undefined && Array.isArray(updateSpecifications)) {
      updateData.specifications = [];
      for (const spec of updateSpecifications) {
        if (spec.nameEn && spec.nameAr && spec.valueEn && spec.valueAr &&
            spec.nameEn.trim() && spec.nameAr.trim() && spec.valueEn.trim() && spec.valueAr.trim()) {
          updateData.specifications.push({
            key: spec.nameEn.trim(),
            keyAr: spec.nameAr.trim(),
            value: spec.valueEn.trim(),
            valueAr: spec.valueAr.trim()
          });
        }
      }
    }

    const updatedProduct = await updateProductWithDetails(updateId, updateData);
    return NextResponse.json({
      success: true,
      data: updatedProduct,
      message: 'Product updated successfully',
      messageAr: 'تم تحديث المنتج بنجاح'
    });
  } catch (error) {
    console.error('Admin Products PUT API Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}

// DELETE - حذف منتج
export async function DELETE(request: NextRequest) {
  try {
    // TODO: إعادة تفعيل المصادقة لاحقاً
    // const user = requireAdminAuth(request);
    // if (!user) {
    //   return NextResponse.json({
    //     success: false,
    //     message: 'Authentication required',
    //     messageAr: 'المصادقة مطلوبة'
    //   }, { status: 401 });
    // }

    const { searchParams } = new URL(request.url);
    const deleteId = searchParams.get('id');

    if (!deleteId || typeof deleteId !== 'string') {
      return NextResponse.json({
        success: false,
        message: 'Product ID is required',
        messageAr: 'معرف المنتج مطلوب'
      }, { status: 400 });
    }

    const deleted = await deleteProductWithDetails(deleteId);

    if (!deleted) {
      return NextResponse.json({
        success: false,
        message: 'Product not found',
        messageAr: 'المنتج غير موجود'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      message: 'Product deleted successfully',
      messageAr: 'تم حذف المنتج بنجاح'
    });
  } catch (error) {
    console.error('Admin Products DELETE API Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}
