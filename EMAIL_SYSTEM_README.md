# نظام البريد الإلكتروني - طلبات التسعير

## 📧 نظرة عامة

تم تطوير نظام متكامل لإرسال طلبات التسعير عبر البريد الإلكتروني باستخدام خدمة Hostinger SMTP. النظام يقوم بإنشاء ملف Excel تلقائياً ويرسله مع تفاصيل الطلب.

## 🚀 الميزات

- ✅ إرسال تلقائي لطلبات التسعير
- ✅ إنشاء ملف Excel مع تفاصيل المنتجات
- ✅ تصميم إيميل احترافي باللغة العربية
- ✅ دعم مرفقات الملفات
- ✅ نظام تسجيل مفصل للأخطاء
- ✅ اختبار إعدادات البريد الإلكتروني

## 🔧 الإعداد السريع

### 1. إعد<PERSON> المجلدات المطلوبة

```bash
npm run setup-dirs
```

### 2. إعداد متغيرات البيئة

أضف المتغيرات التالية إلى ملف `.env.local`:

```env
# إعدادات الإيميل - Hostinger SMTP
EMAIL_USER=<EMAIL>          # الإيميل المرسل
EMAIL_PASS=your-email-password          # كلمة مرور الإيميل
ADMIN_EMAIL=<EMAIL>        # الإيميل المستقبل
SMTP_HOST=smtp.hostinger.com            # خادم SMTP
SMTP_PORT=465                           # منفذ SSL
```

### 3. اختبار الإعدادات

```bash
npm run test-email
```

### 4. تشغيل النظام

```bash
npm run dev
```

## 📋 كيفية عمل النظام

### 1. إرسال طلب تسعير

عندما يقوم العميل بإرسال طلب تسعير:

1. **جمع البيانات:** يتم جمع معلومات العميل والمنتجات المطلوبة
2. **إنشاء ملف Excel:** يتم إنشاء ملف Excel يحتوي على:
   - رقم المنتج
   - اسم المنتج (بالعربية)
   - الكمية المطلوبة
   - عمود فارغ للسعر المقترح
   - عمود للملاحظات

3. **حفظ في قاعدة البيانات:** يتم حفظ الطلب في قاعدة البيانات MySQL
4. **إرسال الإيميل:** يتم إرسال إيميل يحتوي على:
   - معلومات العميل
   - تفاصيل الطلب
   - ملف Excel مرفق

### 2. محتوى الإيميل

الإيميل يحتوي على:

```
📧 من: DROOB HAJER <<EMAIL>>
📬 إلى: <EMAIL>
📎 مرفق: طلب-تسعير-[ID].xlsx

محتوى الإيميل:
- معلومات العميل (الاسم، الهاتف، الإيميل، الشركة)
- تفاصيل الطلب (رقم الطلب، عدد المنتجات، المجموع التقديري)
- قائمة بالمنتجات المطلوبة
```

## 🔍 استكشاف الأخطاء

### رسائل النجاح

```
✅ تم التحقق من إعدادات SMTP بنجاح
✅ تم إرسال الإيميل بنجاح عبر Hostinger SMTP!
🆔 Message ID: [message-id]
```

### رسائل الخطأ الشائعة

#### 1. خطأ المصادقة
```
❌ خطأ في إرسال الإيميل عبر Hostinger SMTP
Error: Invalid login
```
**الحل:** تحقق من EMAIL_USER و EMAIL_PASS

#### 2. خطأ الاتصال
```
❌ خطأ في إرسال الإيميل عبر Hostinger SMTP
Error: Connection timeout
```
**الحل:** تحقق من SMTP_HOST و SMTP_PORT

#### 3. خطأ الشهادة
```
❌ خطأ في إرسال الإيميل عبر Hostinger SMTP
Error: Certificate error
```
**الحل:** تم إضافة `rejectUnauthorized: false` في الكود

## 📁 هيكل الملفات

```
app/api/quote-requests/
├── route.ts                 # API endpoint لطلبات التسعير
├── 
public/uploads/excel/        # مجلد ملفات Excel
├── QR-[timestamp]-[id].xlsx
├── 
scripts/
├── test-email.js           # سكريبت اختبار الإيميل
├── 
docs/
├── HOSTINGER_EMAIL_SETUP.md # دليل إعداد Hostinger
├── EMAIL_SYSTEM_README.md   # هذا الملف
```

## 🔧 الصيانة والمراقبة

### 1. مراقبة السجلات

تحقق من console logs للرسائل التالية:

```javascript
console.log('🔄 محاولة إرسال إيميل عبر Hostinger SMTP...');
console.log('✅ تم التحقق من إعدادات SMTP بنجاح');
console.log('✅ تم إرسال الإيميل بنجاح عبر Hostinger SMTP!');
```

### 2. نسخ احتياطية

- احتفظ بنسخة من إعدادات البريد الإلكتروني
- قم بعمل نسخة احتياطية من ملفات Excel دورياً

### 3. الأمان

- لا تشارك معلومات EMAIL_PASS مع أحد
- استخدم كلمات مرور قوية
- راقب محاولات الدخول غير المصرح بها

## 📞 الدعم الفني

### مشاكل Hostinger
- تواصل مع دعم Hostinger الفني
- تحقق من حالة الخادم في لوحة التحكم

### مشاكل النظام
- تحقق من سجلات الأخطاء في console
- استخدم `npm run test-email` للاختبار
- استخدم `npm run setup-dirs` لإعداد المجلدات
- راجع ملف TROUBLESHOOTING_GUIDE.md للمشاكل الشائعة
- راجع ملف HOSTINGER_EMAIL_SETUP.md لإعداد الإيميل

### أوامر التشخيص السريع
```bash
# إعداد المجلدات
npm run setup-dirs

# اختبار الإيميل
npm run test-email

# تشغيل النظام
npm run dev
```

## 🔄 التحديثات المستقبلية

- [ ] إضافة قوالب إيميل متعددة
- [ ] دعم إرسال إيميلات تأكيد للعملاء
- [ ] نظام إشعارات في الوقت الفعلي
- [ ] تقارير إحصائية لطلبات التسعير

---

**آخر تحديث:** يونيو 2025  
**الإصدار:** 2.0  
**المطور:** فريق DROOB HAJER
