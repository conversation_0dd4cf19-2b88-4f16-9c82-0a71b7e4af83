import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { executeQuerySingle } from '../../../../../lib/database-config';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

interface AdminUser {
  id: number;
  username: string;
  email: string;
  is_active: boolean;
  last_login: Date | null;
  created_at: Date;
  updated_at: Date;
}

// التحقق من صحة JWT token
function verifyToken(token: string) {
  try {
    return jwt.verify(token, JWT_SECRET) as any;
  } catch (error) {
    return null;
  }
}

// استخراج token من الطلب
function extractToken(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  const tokenFromCookie = request.cookies.get('authToken')?.value;
  if (tokenFromCookie) {
    return tokenFromCookie;
  }

  return null;
}

// GET - الحصول على بيانات المستخدم الحالي
export async function GET(request: NextRequest) {
  try {
    // التحقق من المصادقة
    const token = extractToken(request);
    if (!token) {
      return NextResponse.json({
        message: 'غير مصرح لك بالوصول',
        success: false
      }, { status: 401 });
    }

    const decoded = verifyToken(token);
    if (!decoded) {
      return NextResponse.json({
        message: 'رمز المصادقة غير صحيح',
        success: false
      }, { status: 401 });
    }

    // الحصول على بيانات المستخدم من قاعدة البيانات
    const user = await executeQuerySingle<AdminUser>(
      `SELECT id, username, email, is_active, last_login, created_at, updated_at
       FROM admins
       WHERE id = ? AND is_active = 1 AND deleted_at IS NULL`,
      [decoded.userId]
    );

    if (!user) {
      return NextResponse.json({
        message: 'المستخدم غير موجود أو غير نشط',
        success: false
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: 'admin',
        isActive: user.is_active,
        lastLogin: user.last_login,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      }
    });

  } catch (error) {
    console.error('Get current user error:', error);
    return NextResponse.json({
      message: 'حدث خطأ في الخادم',
      success: false
    }, { status: 500 });
  }
}
