const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');

// إعدادات قاعدة البيانات
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'droobhajer_db',
  charset: 'utf8mb4'
};

async function testLogin() {
  let connection = null;
  
  try {
    console.log('🔄 الاتصال بقاعدة البيانات...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
    
    // جلب المستخدم admin
    const [users] = await connection.execute(
      'SELECT * FROM admins WHERE username = ? AND is_active = 1 AND deleted_at IS NULL',
      ['admin']
    );
    
    if (users.length === 0) {
      console.log('❌ المستخدم admin غير موجود');
      console.log('💡 قم بتشغيل: node scripts/create-default-admin.js');
      return;
    }
    
    const admin = users[0];
    console.log('✅ تم العثور على المستخدم admin');
    console.log('📋 بيانات المستخدم:');
    console.log('   ID:', admin.id);
    console.log('   اسم المستخدم:', admin.username);
    console.log('   البريد الإلكتروني:', admin.email);
    console.log('   نشط:', admin.is_active ? 'نعم' : 'لا');
    console.log('   تاريخ الإنشاء:', admin.created_at);
    console.log('   آخر تسجيل دخول:', admin.last_login || 'لم يسجل دخول بعد');
    
    // اختبار كلمة المرور
    console.log('\n🔐 اختبار كلمة المرور...');
    const testPassword = 'admin123';
    const isValid = await bcrypt.compare(testPassword, admin.password_hash);
    
    console.log(`كلمة المرور '${testPassword}': ${isValid ? '✅ صحيحة' : '❌ خاطئة'}`);
    
    if (isValid) {
      console.log('\n🎉 النظام جاهز للاستخدام!');
      console.log('📝 بيانات تسجيل الدخول:');
      console.log('   الرابط: http://localhost:3000/admin/login');
      console.log('   اسم المستخدم: admin');
      console.log('   كلمة المرور: admin123');
    } else {
      console.log('\n❌ هناك مشكلة في كلمة المرور');
      console.log('💡 قم بتشغيل: node scripts/create-default-admin.js');
    }
    
  } catch (error) {
    console.error('❌ خطأ:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 تأكد من أن خادم MySQL يعمل');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('💡 تحقق من بيانات الاتصال بقاعدة البيانات');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('💡 تأكد من وجود قاعدة البيانات droobhajer_db');
    }
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// تشغيل الاختبار
testLogin();
