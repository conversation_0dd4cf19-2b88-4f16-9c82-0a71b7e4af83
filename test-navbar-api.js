const fetch = require('node-fetch');

async function testNavbarAPI() {
  try {
    console.log('🧪 اختبار API الفئات للـ navbar...');
    
    const response = await fetch('http://localhost:3000/api/navbar/categories');
    const result = await response.json();
    
    console.log('📊 النتيجة:', JSON.stringify(result, null, 2));
    
    if (result.success) {
      console.log(`✅ تم جلب ${result.data.length} فئة بنجاح`);
      result.data.forEach(category => {
        console.log(`- ${category.name_ar} (${category.name})`);
        console.log(`  الصورة: ${category.image_url}`);
        console.log(`  الفئات الفرعية: ${category.subcategories?.length || 0}`);
        console.log('');
      });
    } else {
      console.error('❌ فشل في جلب الفئات:', result.message);
    }
  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
  }
}

testNavbarAPI();
