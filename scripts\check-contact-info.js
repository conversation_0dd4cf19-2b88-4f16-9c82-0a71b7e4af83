// سكريبت للتحقق من بيانات contact_info في قاعدة البيانات
const mysql = require('mysql2/promise');

async function checkContactInfo() {
  console.log('🔍 فحص بيانات contact_info...\n');

  // إعدادات قاعدة البيانات
  const dbConfig = {
    host: 'localhost',
    port: 3306,
    user: 'root',
    password: '',
    database: 'droobhajer_db',
    charset: 'utf8mb4'
  };

  let connection;

  try {
    // الاتصال بقاعدة البيانات
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ تم الاتصال بقاعدة البيانات\n');

    // فحص هيكل الجدول
    console.log('📋 هيكل جدول contact_info:');
    const [columns] = await connection.execute('DESCRIBE contact_info');
    columns.forEach(col => {
      console.log(`  - ${col.Field}: ${col.Type} ${col.Null === 'YES' ? '(nullable)' : '(required)'}`);
    });
    console.log('');

    // جلب البيانات
    console.log('📊 البيانات الموجودة:');
    const [rows] = await connection.execute('SELECT * FROM contact_info');
    
    if (rows.length === 0) {
      console.log('📭 لا توجد بيانات في الجدول');
    } else {
      rows.forEach((row, index) => {
        console.log(`\n📄 السجل ${index + 1}:`);
        console.log(`  🆔 ID: ${row.id}`);
        console.log(`  📧 Email: ${row.email || 'غير محدد'}`);
        console.log(`  🔑 Password: ${row.Password ? '***مخفي***' : 'غير محدد'}`);
        console.log(`  🏠 Host: ${row.host || 'غير محدد'}`);
        console.log(`  🔌 Port: ${row.port || 'غير محدد'}`);
        console.log(`  📱 WhatsApp: ${row.whatsapp_number || 'غير محدد'}`);
        console.log(`  📅 Updated: ${row.updated_at || 'غير محدد'}`);
        
        // فحص إذا كانت البيانات مخزنة كـ JSON
        if (row.email && row.email.startsWith('{')) {
          console.log('  ⚠️  تحذير: البيانات مخزنة كـ JSON!');
          try {
            const parsed = JSON.parse(row.email);
            console.log('  📝 البيانات المحللة:', parsed);
          } catch (e) {
            console.log('  ❌ خطأ في تحليل JSON');
          }
        }
      });
    }

    // إحصائيات
    console.log(`\n📈 الإحصائيات:`);
    console.log(`  📊 عدد السجلات: ${rows.length}`);
    
    const validEmails = rows.filter(row => row.email && !row.email.startsWith('{')).length;
    const jsonEmails = rows.filter(row => row.email && row.email.startsWith('{')).length;
    
    console.log(`  ✅ إيميلات صحيحة: ${validEmails}`);
    console.log(`  ⚠️  إيميلات JSON: ${jsonEmails}`);

    // اقتراحات
    if (jsonEmails > 0) {
      console.log('\n💡 اقتراحات الإصلاح:');
      console.log('1. حذف السجلات التي تحتوي على JSON');
      console.log('2. إدراج بيانات جديدة بالتنسيق الصحيح');
      console.log('3. استخدام صفحة إعدادات الإيميل لإدخال البيانات');
    }

  } catch (error) {
    console.error('\n❌ خطأ في فحص البيانات:');
    console.error('📄 تفاصيل الخطأ:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 تم إغلاق الاتصال بقاعدة البيانات');
    }
  }
}

// دالة لإصلاح البيانات الخاطئة
async function fixContactInfo() {
  console.log('🔧 إصلاح بيانات contact_info...\n');

  const dbConfig = {
    host: 'localhost',
    port: 3306,
    user: 'root',
    password: '',
    database: 'droobhajer_db',
    charset: 'utf8mb4'
  };

  let connection;

  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ تم الاتصال بقاعدة البيانات\n');

    // جلب البيانات الحالية
    const [rows] = await connection.execute('SELECT * FROM contact_info');

    if (rows.length === 0) {
      console.log('📭 لا توجد بيانات للإصلاح');
      return;
    }

    // البحث عن السجلات التي تحتوي على JSON في حقل email
    const jsonRecords = rows.filter(row => {
      try {
        JSON.parse(row.email);
        return true;
      } catch {
        return false;
      }
    });

    if (jsonRecords.length === 0) {
      console.log('✅ البيانات صحيحة، لا حاجة للإصلاح');
      return;
    }

    console.log(`🔍 تم العثور على ${jsonRecords.length} سجل يحتاج إصلاح`);

    for (const record of jsonRecords) {
      try {
        // تحليل JSON من حقل email
        const jsonData = JSON.parse(record.email);

        console.log(`\n🔧 إصلاح السجل ${record.id}:`);
        console.log(`  📧 الإيميل الجديد: ${jsonData.email}`);
        console.log(`  🔑 كلمة المرور: ***مخفي***`);
        console.log(`  🏠 المضيف: ${jsonData.host}`);
        console.log(`  🔌 المنفذ: ${jsonData.port}`);

        // تحديث السجل بالبيانات الصحيحة
        await connection.execute(
          'UPDATE contact_info SET email = ?, Password = ?, host = ?, port = ? WHERE id = ?',
          [jsonData.email, jsonData.Password, jsonData.host, jsonData.port, record.id]
        );

        console.log(`  ✅ تم إصلاح السجل ${record.id} بنجاح`);
      } catch (error) {
        console.error(`  ❌ خطأ في إصلاح السجل ${record.id}:`, error.message);
      }
    }

    console.log('\n🎉 تم إصلاح جميع السجلات بنجاح!');

    // التحقق من النتائج
    console.log('\n📊 التحقق من البيانات المُصلحة:');
    const [updatedRows] = await connection.execute('SELECT * FROM contact_info');

    updatedRows.forEach((row, index) => {
      console.log(`\n📄 السجل ${index + 1}:`);
      console.log(`  🆔 ID: ${row.id}`);
      console.log(`  📧 Email: ${row.email}`);
      console.log(`  🔑 Password: ${row.Password ? '***مخفي***' : 'غير محدد'}`);
      console.log(`  🏠 Host: ${row.host}`);
      console.log(`  🔌 Port: ${row.port}`);
    });

  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 تم إغلاق الاتصال بقاعدة البيانات');
    }
  }
}

// تشغيل الدالة المناسبة حسب المعامل
const action = process.argv[2];
if (action === 'fix') {
  fixContactInfo().catch(console.error);
} else {
  checkContactInfo().catch(console.error);
}
