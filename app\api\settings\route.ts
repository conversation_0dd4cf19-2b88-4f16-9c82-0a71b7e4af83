import { NextRequest, NextResponse } from 'next/server';
import { getSiteSettings, saveSiteSettings } from '../../../data/settings';
import { readSettingsFromFile, writeSettingsToFile, checkFileWritePermissions } from '../../../lib/settings-file';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

// التحقق من صحة JWT token
function verifyToken(token: string) {
  try {
    return jwt.verify(token, JWT_SECRET) as any;
  } catch (error) {
    return null;
  }
}

// استخراج token من الطلب
function extractToken(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  const tokenFromCookie = request.cookies.get('authToken')?.value;
  if (tokenFromCookie) {
    return tokenFromCookie;
  }

  return null;
}

export async function GET(request: NextRequest) {
  try {
    // السماح بقراءة الإعدادات بدون مصادقة للاستخدام العام
    console.log('📖 قراءة الإعدادات...');

    // محاولة قراءة من الملف أولاً، ثم localStorage كبديل
    let settings;
    try {
      settings = readSettingsFromFile();
      console.log('✅ تم تحميل الإعدادات من الملف');
    } catch (error) {
      console.log('⚠️ فشل تحميل الإعدادات من الملف، استخدام localStorage');
      settings = getSiteSettings();
    }

    return NextResponse.json(settings);
  } catch (error) {
    console.error('❌ خطأ في GET API الإعدادات:', error);
    return NextResponse.json({
      success: false,
      message: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // التحقق من المصادقة للتعديل
    const token = extractToken(request);
    if (!token) {
      return NextResponse.json({
        success: false,
        message: 'Authentication required',
        messageAr: 'المصادقة مطلوبة'
      }, { status: 401 });
    }

    const decoded = verifyToken(token);
    if (!decoded) {
      return NextResponse.json({
        success: false,
        message: 'Invalid token',
        messageAr: 'رمز المصادقة غير صحيح'
      }, { status: 401 });
    }

    const updates = await request.json();
    console.log('🔄 تحديث إعدادات الموقع:', Object.keys(updates));

    // التحقق من صلاحيات الكتابة
    if (!checkFileWritePermissions()) {
      return NextResponse.json({
        success: false,
        message: 'File write permissions denied',
        messageAr: 'لا توجد صلاحيات كتابة الملف'
      }, { status: 500 });
    }

    // قراءة الإعدادات الحالية من الملف
    let currentSettings;
    try {
      currentSettings = readSettingsFromFile();
    } catch (error) {
      console.log('⚠️ فشل قراءة الملف، استخدام الإعدادات الافتراضية');
      currentSettings = getSiteSettings();
    }

    // دمج الإعدادات الجديدة مع الحالية
    const mergedSettings = { ...currentSettings, ...updates };

    // حفظ في الملف أولاً
    const fileSaved = writeSettingsToFile(mergedSettings);
    if (!fileSaved) {
      return NextResponse.json({
        success: false,
        message: 'Failed to save settings to file',
        messageAr: 'فشل في حفظ الإعدادات في الملف'
      }, { status: 500 });
    }

    // حفظ في localStorage أيضاً للتوافق
    saveSiteSettings(mergedSettings);

    // قراءة الإعدادات المحدثة للتأكد
    const updatedSettings = readSettingsFromFile();

    console.log('✅ تم تحديث إعدادات الموقع بنجاح في الملف و localStorage');

    return NextResponse.json({
      success: true,
      message: 'Settings updated successfully',
      messageAr: 'تم تحديث الإعدادات بنجاح',
      ...updatedSettings
    });

  } catch (error) {
    console.error('❌ خطأ في PUT API الإعدادات:', error);
    return NextResponse.json({
      success: false,
      message: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    }, { status: 500 });
  }
}
