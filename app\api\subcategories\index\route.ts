import { NextRequest, NextResponse } from 'next/server';
import { getSubcategories, getSubcategoriesByCategory, addSubcategory } from '@/lib/mysql-database';
import { Subcategory } from '@/types/mysql-database';
import { v4 as uuidv4 } from 'uuid';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const categoryId = searchParams.get('categoryId');

    let subcategories: Subcategory[];
    if (categoryId && typeof categoryId === 'string') {
      subcategories = await getSubcategoriesByCategory(categoryId);
    } else {
      subcategories = await getSubcategories();
    }

    return NextResponse.json({
      success: true,
      data: subcategories
    });
  } catch (error) {
    console.error('Subcategories GET API Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, nameAr, categoryId: catId, description, descriptionAr, image, isActive } = body;

    if (!name || !nameAr || !catId) {
      return NextResponse.json({
        success: false,
        message: 'Name, Arabic name, and category ID are required',
        messageAr: 'الاسم والاسم بالعربية ومعرف الفئة مطلوبة'
      }, { status: 400 });
    }

    const subcategoryData = {
      id: uuidv4(),
      name,
      name_ar: nameAr,
      category_id: catId,
      description: description || null,
      description_ar: descriptionAr || null,
      image: image || null,
      is_active: isActive !== undefined ? isActive : true
    };

    const newSubcategory = await addSubcategory(subcategoryData);
    return NextResponse.json({
      success: true,
      data: newSubcategory,
      message: 'Subcategory created successfully',
      messageAr: 'تم إنشاء الفئة الفرعية بنجاح'
    }, { status: 201 });
  } catch (error) {
    console.error('Subcategories POST API Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}
