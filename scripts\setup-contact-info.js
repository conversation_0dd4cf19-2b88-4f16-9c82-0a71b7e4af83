// سكريبت إعداد معلومات الاتصال في قاعدة البيانات
const mysql = require('mysql2/promise');
require('dotenv').config({ path: '.env.local' });

async function setupContactInfo() {
  console.log('🔄 بدء إعداد معلومات الاتصال...\n');

  // إعدادات قاعدة البيانات
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'droobhajer_db',
    charset: 'utf8mb4'
  };

  console.log('📋 إعدادات قاعدة البيانات:');
  console.log(`🏠 Host: ${dbConfig.host}`);
  console.log(`🔌 Port: ${dbConfig.port}`);
  console.log(`👤 User: ${dbConfig.user}`);
  console.log(`🗄️  Database: ${dbConfig.database}\n`);

  let connection;

  try {
    // الاتصال بقاعدة البيانات
    console.log('🔄 الاتصال بقاعدة البيانات...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح\n');

    // التحقق من وجود جدول contact_info
    console.log('🔍 التحقق من وجود جدول contact_info...');
    const [tables] = await connection.execute(
      "SHOW TABLES LIKE 'contact_info'"
    );

    if (tables.length === 0) {
      console.log('❌ جدول contact_info غير موجود');
      console.log('💡 يرجى تشغيل ملف SQL لإنشاء الجداول أولاً');
      return;
    }

    console.log('✅ جدول contact_info موجود\n');

    // التحقق من وجود بيانات
    console.log('🔍 التحقق من البيانات الموجودة...');
    const [existingData] = await connection.execute(
      'SELECT * FROM contact_info ORDER BY id DESC LIMIT 1'
    );

    if (existingData.length > 0) {
      console.log('📧 البيانات الموجودة:');
      console.log(`📧 الإيميل: ${existingData[0].email || 'غير محدد'}`);
      console.log(`📱 الواتساب: ${existingData[0].whatsapp_number || 'غير محدد'}`);
      console.log(`📅 آخر تحديث: ${existingData[0].updated_at}\n`);

      console.log('❓ هل تريد تحديث البيانات الموجودة؟');
      console.log('💡 يمكنك تحديث البيانات من لوحة التحكم: /admin/email-settings');
    } else {
      console.log('📭 لا توجد بيانات، سيتم إدراج بيانات افتراضية...\n');

      // إدراج بيانات افتراضية
      const defaultEmail = process.env.EMAIL_USER || '<EMAIL>';
      const defaultWhatsapp = '+966501234567';

      console.log('📝 إدراج البيانات الافتراضية...');
      console.log(`📧 الإيميل الافتراضي: ${defaultEmail}`);
      console.log(`📱 الواتساب الافتراضي: ${defaultWhatsapp}`);

      await connection.execute(
        'INSERT INTO contact_info (email, whatsapp_number) VALUES (?, ?)',
        [defaultEmail, defaultWhatsapp]
      );

      console.log('✅ تم إدراج البيانات الافتراضية بنجاح!\n');

      // عرض البيانات المدرجة
      const [newData] = await connection.execute(
        'SELECT * FROM contact_info ORDER BY id DESC LIMIT 1'
      );

      if (newData.length > 0) {
        console.log('📊 البيانات المدرجة:');
        console.log(`🆔 ID: ${newData[0].id}`);
        console.log(`📧 الإيميل: ${newData[0].email}`);
        console.log(`📱 الواتساب: ${newData[0].whatsapp_number}`);
        console.log(`📅 تاريخ الإنشاء: ${newData[0].updated_at}`);
      }
    }

    console.log('\n' + '='.repeat(50));
    console.log('🎉 تم إعداد معلومات الاتصال بنجاح!');
    console.log('\n📋 الخطوات التالية:');
    console.log('1. اذهب إلى لوحة التحكم: /admin/email-settings');
    console.log('2. تحديث الإيميل ورقم الواتساب');
    console.log('3. اختبار إرسال الإيميل');
    console.log('4. تأكد من إعداد كلمة مرور الإيميل في .env.local');

  } catch (error) {
    console.error('\n❌ خطأ في إعداد معلومات الاتصال:');
    console.error('📄 تفاصيل الخطأ:', error.message);

    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n💡 نصائح لحل مشكلة الوصول:');
      console.log('1. تأكد من صحة اسم المستخدم وكلمة المرور');
      console.log('2. تأكد من تشغيل خادم MySQL');
      console.log('3. تحقق من إعدادات قاعدة البيانات في .env.local');
    } else if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 نصائح لحل مشكلة الاتصال:');
      console.log('1. تأكد من تشغيل خادم MySQL');
      console.log('2. تحقق من المنفذ والعنوان في .env.local');
      console.log('3. تأكد من إعدادات الجدار الناري');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('\n💡 نصائح لحل مشكلة قاعدة البيانات:');
      console.log('1. تأكد من وجود قاعدة البيانات droobhajer_db');
      console.log('2. قم بإنشاء قاعدة البيانات إذا لم تكن موجودة');
      console.log('3. تشغيل ملف SQL لإنشاء الجداول');
    }
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 تم إغلاق الاتصال بقاعدة البيانات');
    }
  }
}

// تشغيل السكريبت
setupContactInfo().catch(console.error);
