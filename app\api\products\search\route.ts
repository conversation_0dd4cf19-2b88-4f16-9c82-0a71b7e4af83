import { NextRequest, NextResponse } from 'next/server';
import { searchProductsInDatabase } from '../../../../lib/mysql-database';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const q = searchParams.get('q');

    if (!q || typeof q !== 'string') {
      return NextResponse.json({
        success: false,
        error: 'Search query is required',
        messageAr: 'استعلام البحث مطلوب'
      }, { status: 400 });
    }

    const products = await searchProductsInDatabase(q);
    return NextResponse.json({
      success: true,
      data: products
    });
  } catch (error) {
    console.error('Products Search API Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}
