const mysql = require('mysql2/promise');

async function checkImages() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root', 
      password: '',
      database: 'droobhajer_db'
    });
    
    console.log('=== Categories ===');
    const [categories] = await connection.execute('SELECT id, name, name_ar, image_url FROM categories WHERE deleted_at IS NULL LIMIT 3');
    categories.forEach(cat => {
      console.log(`ID: ${cat.id}`);
      console.log(`Name: ${cat.name}`);
      console.log(`Name AR: ${cat.name_ar}`);
      console.log(`Image: ${cat.image_url || 'NULL'}`);
      console.log('---');
    });

    // إضافة صور للفئات إذا لم تكن موجودة
    console.log('\n=== Adding sample images to categories ===');
    await connection.execute(`
      UPDATE categories
      SET image_url = '/uploads/category-fb.jpg'
      WHERE name = 'F&B' AND (image_url IS NULL OR image_url = '')
    `);

    await connection.execute(`
      UPDATE categories
      SET image_url = '/uploads/category-kitchen.jpg'
      WHERE name LIKE '%Kitchen%' AND (image_url IS NULL OR image_url = '')
    `);

    console.log('Sample images added to categories');
    
    console.log('\n=== Subcategories Table Structure ===');
    const [columns] = await connection.execute('DESCRIBE subcategories');
    columns.forEach(col => console.log(`${col.Field}: ${col.Type}`));

    console.log('\n=== Subcategories Data ===');
    const [subcategories] = await connection.execute('SELECT * FROM subcategories WHERE deleted_at IS NULL LIMIT 3');
    subcategories.forEach(sub => {
      console.log(`ID: ${sub.id}`);
      console.log(`Name: ${sub.name}`);
      console.log(`Name AR: ${sub.name_ar}`);
      console.log(`Image URL: ${sub.image_url || 'NULL'}`);
      console.log('---');
    });

    // إضافة صور للفئات الفرعية
    console.log('\n=== Adding sample images to subcategories ===');
    await connection.execute(`
      UPDATE subcategories
      SET image_url = '/uploads/subcategory-chinaware.jpg'
      WHERE name = 'Chinaware' AND (image_url IS NULL OR image_url = '')
    `);

    console.log('Sample images added to subcategories');
    
    console.log('\n=== Product Images ===');
    const [images] = await connection.execute('SELECT product_id, image_url FROM product_images LIMIT 3');
    images.forEach(img => {
      console.log(`Product ID: ${img.product_id}`);
      console.log(`Image: ${img.image_url}`);
      console.log('---');
    });
    
    await connection.end();
  } catch (error) {
    console.error('Error:', error.message);
  }
}

checkImages();
