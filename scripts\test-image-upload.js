const fs = require('fs');
const path = require('path');

console.log('🖼️ اختبار نظام رفع صور الهيرو...\n');

// مسارات المجلدات والملفات
const uploadsDir = path.join(__dirname, '..', 'public', 'uploads');
const imagesDir = path.join(__dirname, '..', 'public', 'images');
const placeholderPath = path.join(imagesDir, 'placeholder.jpg');

console.log('📁 فحص المجلدات المطلوبة:');

// فحص مجلد uploads
if (fs.existsSync(uploadsDir)) {
  console.log('✅ مجلد uploads موجود');
  
  // عرض الملفات الموجودة
  try {
    const files = fs.readdirSync(uploadsDir);
    const imageFiles = files.filter(file => 
      /\.(jpg|jpeg|png|gif|webp)$/i.test(file)
    );
    
    console.log(`   📊 عدد الصور الموجودة: ${imageFiles.length}`);
    
    if (imageFiles.length > 0) {
      console.log('   📋 أحدث 5 صور:');
      imageFiles.slice(-5).forEach(file => {
        const filePath = path.join(uploadsDir, file);
        const stats = fs.statSync(filePath);
        const sizeKB = Math.round(stats.size / 1024);
        console.log(`      • ${file} (${sizeKB} KB)`);
      });
    }
  } catch (error) {
    console.log('❌ خطأ في قراءة مجلد uploads:', error.message);
  }
} else {
  console.log('❌ مجلد uploads غير موجود');
  console.log('💡 سيتم إنشاؤه تلقائياً عند أول رفع');
}

// فحص مجلد images
if (fs.existsSync(imagesDir)) {
  console.log('✅ مجلد images موجود');
} else {
  console.log('❌ مجلد images غير موجود');
  try {
    fs.mkdirSync(imagesDir, { recursive: true });
    console.log('✅ تم إنشاء مجلد images');
  } catch (error) {
    console.log('❌ فشل في إنشاء مجلد images:', error.message);
  }
}

// فحص ملف placeholder
if (fs.existsSync(placeholderPath)) {
  console.log('✅ ملف placeholder.jpg موجود');
} else {
  console.log('❌ ملف placeholder.jpg غير موجود');
  console.log('💡 سيتم إنشاؤه...');
  
  // إنشاء ملف placeholder بسيط
  const placeholderSVG = `<svg width="400" height="200" viewBox="0 0 400 200" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="400" height="200" fill="#F3F4F6"/>
<rect x="175" y="75" width="50" height="50" fill="#9CA3AF"/>
<polygon points="200,90 210,105 190,105" fill="#F9FAFB"/>
<text x="200" y="150" font-family="Arial, sans-serif" font-size="14" fill="#6B7280" text-anchor="middle">صورة</text>
</svg>`;

  try {
    fs.writeFileSync(placeholderPath, placeholderSVG);
    console.log('✅ تم إنشاء ملف placeholder.jpg');
  } catch (error) {
    console.log('❌ فشل في إنشاء ملف placeholder:', error.message);
  }
}

console.log('\n🧪 اختبار صلاحيات الكتابة:');

// اختبار كتابة ملف تجريبي في uploads
try {
  const testImagePath = path.join(uploadsDir, 'test-write.txt');
  fs.writeFileSync(testImagePath, 'اختبار الكتابة');
  console.log('✅ صلاحيات الكتابة في uploads متوفرة');
  
  // حذف الملف التجريبي
  fs.unlinkSync(testImagePath);
  console.log('✅ صلاحيات الحذف في uploads متوفرة');
} catch (error) {
  console.log('❌ مشكلة في صلاحيات uploads:', error.message);
}

console.log('\n📋 فحص ملفات النظام:');

// فحص ملفات API
const apiFiles = [
  'src/pages/api/upload-image.ts',
  'src/pages/api/delete-image.ts'
];

apiFiles.forEach(file => {
  const filePath = path.join(__dirname, '..', file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file} موجود`);
  } else {
    console.log(`❌ ${file} غير موجود`);
  }
});

// فحص مكون React
const componentPath = path.join(__dirname, '..', 'src/components/admin/HeroImageUpload.tsx');
if (fs.existsSync(componentPath)) {
  console.log('✅ مكون HeroImageUpload.tsx موجود');
} else {
  console.log('❌ مكون HeroImageUpload.tsx غير موجود');
}

console.log('\n📊 إحصائيات المجلد:');

try {
  const stats = fs.statSync(uploadsDir);
  console.log(`📁 مجلد uploads:`);
  console.log(`   📅 تاريخ الإنشاء: ${stats.birthtime.toLocaleDateString('ar-SA')}`);
  console.log(`   📝 آخر تعديل: ${stats.mtime.toLocaleDateString('ar-SA')}`);
  
  // حساب المساحة المستخدمة
  const files = fs.readdirSync(uploadsDir);
  let totalSize = 0;
  
  files.forEach(file => {
    const filePath = path.join(uploadsDir, file);
    const fileStats = fs.statSync(filePath);
    totalSize += fileStats.size;
  });
  
  const totalSizeMB = (totalSize / (1024 * 1024)).toFixed(2);
  console.log(`   💾 المساحة المستخدمة: ${totalSizeMB} MB`);
  console.log(`   📁 عدد الملفات: ${files.length}`);
  
} catch (error) {
  console.log('❌ خطأ في قراءة إحصائيات المجلد:', error.message);
}

console.log('\n🔧 تعليمات الاختبار:');
console.log('1. تأكد من تشغيل الخادم: npm run dev');
console.log('2. اذهب إلى: http://localhost:3000/admin/settings');
console.log('3. اختر تبويب "صور الهيرو"');
console.log('4. اضغط على "إضافة صور" وارفع صورة');
console.log('5. تحقق من ظهور الصورة في القائمة');
console.log('6. تحقق من حفظ الصورة في:', uploadsDir);

console.log('\n💡 نصائح:');
console.log('• استخدم صور بحجم أقل من 10MB');
console.log('• الصيغ المدعومة: JPG, PNG, WebP, GIF');
console.log('• يمكن رفع حتى 5 صور في نفس الوقت');
console.log('• الصور تُحفظ بأسماء فريدة تلقائياً');

console.log('\n✨ النظام جاهز للاختبار!');
