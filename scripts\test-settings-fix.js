const fs = require('fs');
const path = require('path');

console.log('🔧 اختبار إصلاح نظام الإعدادات...\n');

// مسارات الملفات
const settingsJsonPath = path.join(__dirname, '..', 'src', 'data', 'settings.json');
const settingsTsPath = path.join(__dirname, '..', 'src', 'data', 'settings.ts');
const apiPath = path.join(__dirname, '..', 'src', 'pages', 'api', 'settings.ts');

console.log('📁 فحص الملفات المطلوبة:');

// فحص ملف settings.json
if (fs.existsSync(settingsJsonPath)) {
  console.log('✅ ملف settings.json موجود');
  try {
    const content = fs.readFileSync(settingsJsonPath, 'utf8');
    const settings = JSON.parse(content);
    console.log('   📋 اسم الموقع:', settings.siteName || 'غير محدد');
    console.log('   📧 البريد الإلكتروني:', settings.contactEmail || 'غير محدد');
  } catch (error) {
    console.log('❌ خطأ في قراءة ملف settings.json:', error.message);
  }
} else {
  console.log('❌ ملف settings.json غير موجود');
  console.log('💡 سيتم إنشاؤه تلقائياً عند أول حفظ');
}

// فحص ملف settings.ts
if (fs.existsSync(settingsTsPath)) {
  console.log('✅ ملف settings.ts موجود');
} else {
  console.log('❌ ملف settings.ts غير موجود');
}

// فحص ملف API
if (fs.existsSync(apiPath)) {
  console.log('✅ ملف API settings.ts موجود');
} else {
  console.log('❌ ملف API settings.ts غير موجود');
}

console.log('\n🧪 اختبار كتابة الملف:');

// اختبار كتابة ملف تجريبي
const testData = {
  siteName: 'VidMeet Test',
  siteNameAr: 'فيد ميت تجريبي',
  contactEmail: '<EMAIL>',
  testTimestamp: new Date().toISOString()
};

try {
  // التأكد من وجود المجلد
  const settingsDir = path.dirname(settingsJsonPath);
  if (!fs.existsSync(settingsDir)) {
    fs.mkdirSync(settingsDir, { recursive: true });
    console.log('📁 تم إنشاء مجلد البيانات');
  }

  // كتابة ملف تجريبي
  const testPath = path.join(settingsDir, 'test-write.json');
  fs.writeFileSync(testPath, JSON.stringify(testData, null, 2));
  console.log('✅ اختبار الكتابة نجح');

  // قراءة الملف للتأكد
  const readData = JSON.parse(fs.readFileSync(testPath, 'utf8'));
  console.log('✅ اختبار القراءة نجح');
  console.log('   📋 البيانات المقروءة:', readData.siteName);

  // حذف الملف التجريبي
  fs.unlinkSync(testPath);
  console.log('🗑️ تم حذف الملف التجريبي');

} catch (error) {
  console.log('❌ فشل اختبار الكتابة:', error.message);
  console.log('💡 تحقق من صلاحيات الكتابة في المجلد');
}

console.log('\n📋 تعليمات الاختبار:');
console.log('1. تأكد من تشغيل الخادم: npm run dev');
console.log('2. اذهب إلى: http://localhost:3000/admin/settings');
console.log('3. تحقق من مكون "حالة نظام الإعدادات"');
console.log('4. غير أي إعداد واحفظ');
console.log('5. تحقق من ملف:', settingsJsonPath);
console.log('6. أعد تحميل الصفحة وتأكد من بقاء التغييرات');

console.log('\n🔍 نصائح استكشاف الأخطاء:');
console.log('- إذا لم تُحفظ التغييرات، تحقق من console المتصفح');
console.log('- إذا ظهرت أخطاء API، تحقق من terminal الخادم');
console.log('- إذا لم ينعكس التغيير على الموقع، امسح cache المتصفح');
console.log('- تحقق من أن ملف settings.json يتم تحديثه فعلياً');

console.log('\n✨ النظام جاهز للاختبار!');
