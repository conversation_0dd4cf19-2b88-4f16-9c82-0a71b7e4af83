import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import { generateToken } from '../../../../lib/auth';
import { executeQuerySingle } from '../../../../lib/database-config';
import { serialize } from 'cookie';
import {
  checkRateLimitByIP,
  RATE_LIMIT_CONFIGS,
  getClientIP,
  isIPBlocked,
  logSuspiciousActivity
} from '../../../../lib/rate-limiter';

interface AdminUser {
  id: number;
  username: string;
  email: string;
  password_hash: string;
  is_active: boolean;
  last_login: Date | null;
  created_at: Date;
  updated_at: Date;
}

interface LoginRequest {
  username: string;
  password: string;
  rememberMe?: boolean;
}

// POST - تسجيل دخول المدير
export async function POST(request: NextRequest) {
  try {
    const body = await request.json() as LoginRequest;
    const { username, password, rememberMe = false } = body;

    // التحقق من وجود البيانات المطلوبة
    if (!username || !password) {
      return NextResponse.json({
        success: false,
        message: 'اسم المستخدم وكلمة المرور مطلوبان',
        messageAr: 'اسم المستخدم وكلمة المرور مطلوبان'
      }, { status: 400 });
    }

    // الحصول على IP العميل
    const clientIP = getClientIP(request);
    console.log(`🔐 محاولة تسجيل دخول من IP: ${clientIP} للمستخدم: ${username}`);

    // التحقق من حظر IP
    if (isIPBlocked(clientIP)) {
      console.log(`🚫 IP محظور: ${clientIP}`);
      logSuspiciousActivity(clientIP, 'blocked_ip_login_attempt', 'Blocked IP attempting login', request.headers.get('user-agent') || '');

      return NextResponse.json({
        success: false,
        message: 'تم حظر عنوان IP الخاص بك مؤقتاً بسبب محاولات تسجيل دخول مشبوهة',
        messageAr: 'تم حظر عنوان IP الخاص بك مؤقتاً بسبب محاولات تسجيل دخول مشبوهة'
      }, { status: 429 });
    }

    // التحقق من معدل المحاولات
    const rateLimitResult = checkRateLimitByIP(clientIP, 'admin_login', RATE_LIMIT_CONFIGS.LOGIN);
    if (!rateLimitResult.allowed) {
      console.log(`⚠️ تجاوز معدل المحاولات للـ IP: ${clientIP}`);
      logSuspiciousActivity(clientIP, 'rate_limit_exceeded', 'Rate limit exceeded for admin login', request.headers.get('user-agent') || '');

      return NextResponse.json({
        success: false,
        message: `تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول. حاول مرة أخرى بعد ${Math.ceil(rateLimitResult.resetTime / 60)} دقيقة`,
        messageAr: `تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول. حاول مرة أخرى بعد ${Math.ceil(rateLimitResult.resetTime / 60)} دقيقة`,
        retryAfter: rateLimitResult.resetTime
      }, { status: 429 });
    }

    // البحث عن المستخدم في قاعدة البيانات
    const user = await executeQuerySingle<AdminUser>(
      `SELECT id, username, email, password_hash, is_active, last_login, created_at, updated_at 
       FROM admins 
       WHERE username = ? AND deleted_at IS NULL`,
      [username]
    );

    // التحقق من وجود المستخدم
    if (!user) {
      console.log(`❌ مستخدم غير موجود: ${username} من IP: ${clientIP}`);
      logSuspiciousActivity(clientIP, 'invalid_username', 'Invalid username attempt', request.headers.get('user-agent') || '');

      return NextResponse.json({
        success: false,
        message: 'اسم المستخدم أو كلمة المرور غير صحيحة',
        messageAr: 'اسم المستخدم أو كلمة المرور غير صحيحة'
      }, { status: 401 });
    }

    // التحقق من حالة المستخدم
    if (!user.is_active) {
      console.log(`🚫 مستخدم غير نشط: ${username} من IP: ${clientIP}`);
      logSuspiciousActivity(clientIP, 'inactive_user_login', 'Inactive user login attempt', request.headers.get('user-agent') || '');

      return NextResponse.json({
        success: false,
        message: 'حساب المستخدم غير نشط. يرجى التواصل مع المدير',
        messageAr: 'حساب المستخدم غير نشط. يرجى التواصل مع المدير'
      }, { status: 403 });
    }

    // التحقق من كلمة المرور
    const isValidPassword = await bcrypt.compare(password, user.password_hash);

    if (!isValidPassword) {
      console.log(`❌ كلمة مرور خاطئة للمستخدم: ${username} من IP: ${clientIP}`);
      logSuspiciousActivity(clientIP, 'invalid_password', 'Invalid password attempt', request.headers.get('user-agent') || '');

      return NextResponse.json({
        success: false,
        message: 'اسم المستخدم أو كلمة المرور غير صحيحة',
        messageAr: 'اسم المستخدم أو كلمة المرور غير صحيحة'
      }, { status: 401 });
    }

    // تحديث وقت آخر تسجيل دخول
    await executeQuerySingle(
      'UPDATE admins SET last_login = NOW(), updated_at = NOW() WHERE id = ?',
      [user.id]
    );

    // إنشاء JWT token
    const token = generateToken({
      id: user.id.toString(),
      username: user.username,
      email: user.email,
      role: 'admin',
      lastLogin: user.last_login || new Date()
    });

    console.log(`✅ تسجيل دخول ناجح للمستخدم: ${username} من IP: ${clientIP}`);

    // إعداد cookie
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax' as const,
      maxAge: rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60, // 30 days or 24 hours
      path: '/'
    };

    const cookie = serialize('admin-token', token, cookieOptions);

    // إنشاء الاستجابة
    const response = NextResponse.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      messageAr: 'تم تسجيل الدخول بنجاح',
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: 'admin',
        lastLogin: user.last_login
      },
      token
    });

    // إضافة cookie إلى الاستجابة
    response.headers.set('Set-Cookie', cookie);

    return response;

  } catch (error) {
    console.error('❌ خطأ في تسجيل الدخول:', error);
    
    return NextResponse.json({
      success: false,
      message: 'حدث خطأ في الخادم. يرجى المحاولة مرة أخرى',
      messageAr: 'حدث خطأ في الخادم. يرجى المحاولة مرة أخرى'
    }, { status: 500 });
  }
}
