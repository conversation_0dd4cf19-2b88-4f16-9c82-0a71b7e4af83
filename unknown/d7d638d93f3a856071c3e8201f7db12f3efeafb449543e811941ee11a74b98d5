const mysql = require('mysql2/promise');

async function checkCategories() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root', 
      password: '',
      database: 'droobhajer_db'
    });
    
    console.log('=== Categories After Update ===');
    const [categories] = await connection.execute('SELECT id, name, name_ar, image_url FROM categories WHERE deleted_at IS NULL');
    categories.forEach(cat => {
      console.log('ID:', cat.id);
      console.log('Name:', cat.name);
      console.log('Name AR:', cat.name_ar);
      console.log('Image:', cat.image_url || 'NULL');
      console.log('---');
    });
    
    await connection.end();
  } catch (error) {
    console.error('Error:', error.message);
  }
}

checkCategories();
