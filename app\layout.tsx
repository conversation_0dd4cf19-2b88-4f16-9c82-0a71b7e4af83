import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Toaster } from 'react-hot-toast'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'DROOB HAJER  - معدات الضيافة',
  description: 'موقع متخصص في معدات المطاعم والفنادق',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // إخفاء تحذيرات React المتعلقة بـ sticky/fixed positioning
  if (typeof window !== 'undefined') {
    const originalConsoleWarn = console.warn;
    console.warn = (...args) => {
      if (args[0]?.includes?.('Skipping auto-scroll behavior due to')) {
        return;
      }
      originalConsoleWarn.apply(console, args);
    };
  }

  return (
    <html>
      <head>
        <link
          href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap"
          rel="stylesheet"
        />
        <link
          href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap"
          rel="stylesheet"
        />
        <link
          href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css"
          rel="stylesheet"
        />
      </head>
      <body className={inter.className} suppressHydrationWarning={true}>
        {children}
        <Toaster
          position="top-center"
          reverseOrder={false}
          gutter={8}
          containerClassName=""
          containerStyle={{}}
          toastOptions={{
            // إعدادات افتراضية لجميع التوستات
            className: '',
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
              fontFamily: 'Tajawal, Arial, sans-serif',
              fontSize: '14px',
              borderRadius: '12px',
              padding: '16px 20px',
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2)',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(10px)',
            },
            // إعدادات للنجاح
            success: {
              duration: 5000,
              style: {
                background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                color: '#fff',
                border: '1px solid rgba(16, 185, 129, 0.3)',
              },
              iconTheme: {
                primary: '#fff',
                secondary: '#10b981',
              },
            },
            // إعدادات للخطأ
            error: {
              duration: 6000,
              style: {
                background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
                color: '#fff',
                border: '1px solid rgba(239, 68, 68, 0.3)',
              },
              iconTheme: {
                primary: '#fff',
                secondary: '#ef4444',
              },
            },
            // إعدادات للتحميل
            loading: {
              duration: Infinity,
              style: {
                background: 'linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)',
                color: '#fff',
                border: '1px solid rgba(59, 130, 246, 0.3)',
              },
            },
          }}
        />
      </body>
    </html>
  )
}
