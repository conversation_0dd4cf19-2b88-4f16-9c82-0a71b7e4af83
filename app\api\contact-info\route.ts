import { NextRequest, NextResponse } from 'next/server';
import { getContactInfo, updateContactInfo } from '@/lib/mysql-database';

export async function GET(_request: NextRequest) {
  try {
    console.log('🔄 جلب معلومات التواصل...');
    
    const contactInfo = await getContactInfo();
    
    if (!contactInfo) {
      // إرجاع القيم الافتراضية إذا لم توجد بيانات
      return NextResponse.json({
        success: true,
        data: {
          email: '',
          hasPassword: false,
          host: 'smtp.hostinger.com',
          port: 465
        }
      });
    }

    // إخفاء كلمة المرور في الاستجابة
    return NextResponse.json({
      success: true,
      data: {
        email: contactInfo.email || '',
        hasPassword: !!contactInfo.Password,
        host: contactInfo.host,
        port: contactInfo.port
      }
    });

  } catch (error: unknown) {
    console.error('❌ خطأ في جلب معلومات التواصل:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to fetch contact info',
      messageAr: 'فشل في جلب معلومات التواصل',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 تحديث معلومات التواصل...');
    
    const body = await request.json();
    const { email, password, host, port } = body;

    // التحقق من البيانات المطلوبة
    if (!email || !email.includes('@')) {
      return NextResponse.json({
        success: false,
        message: 'Valid email is required',
        messageAr: 'يرجى إدخال إيميل صحيح'
      }, { status: 400 });
    }

    if (!password) {
      return NextResponse.json({
        success: false,
        message: 'Password is required',
        messageAr: 'كلمة المرور مطلوبة'
      }, { status: 400 });
    }

    // تحديث معلومات التواصل
    const contactInfo = {
      email,
      Password: password,
      host: host || 'smtp.hostinger.com',
      port: port || 465
    };

    const success = await updateContactInfo(contactInfo);

    if (success) {
      console.log('✅ تم تحديث معلومات التواصل بنجاح');
      return NextResponse.json({
        success: true,
        message: 'Contact info updated successfully',
        messageAr: 'تم تحديث معلومات التواصل بنجاح'
      });
    } else {
      throw new Error('Failed to update contact info');
    }

  } catch (error: unknown) {
    console.error('❌ خطأ في تحديث معلومات التواصل:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to update contact info',
      messageAr: 'فشل في تحديث معلومات التواصل',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
