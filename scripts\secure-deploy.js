const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// ألوان للطباعة
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// خطوات النشر الآمن
class SecureDeployment {
  constructor() {
    this.steps = [];
    this.currentStep = 0;
  }

  // إضافة خطوة
  addStep(name, action) {
    this.steps.push({ name, action });
  }

  // تشغيل خطوة
  async runStep(step) {
    this.currentStep++;
    colorLog('blue', `\n[${this.currentStep}/${this.steps.length}] ${step.name}...`);
    
    try {
      await step.action();
      colorLog('green', `✅ ${step.name} - مكتمل`);
    } catch (error) {
      colorLog('red', `❌ ${step.name} - فشل: ${error.message}`);
      throw error;
    }
  }

  // تشغيل جميع الخطوات
  async deploy() {
    colorLog('bold', '🚀 بدء عملية النشر الآمن...\n');
    
    for (const step of this.steps) {
      await this.runStep(step);
    }
    
    colorLog('green', '\n🎉 تم النشر بنجاح!');
  }

  // فحص المتطلبات الأساسية
  checkPrerequisites() {
    return new Promise((resolve, reject) => {
      try {
        // فحص Node.js
        const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim();
        colorLog('blue', `Node.js version: ${nodeVersion}`);
        
        // فحص npm
        const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
        colorLog('blue', `npm version: ${npmVersion}`);
        
        // فحص وجود الملفات المطلوبة
        const requiredFiles = [
          'package.json',
          'next.config.js',
          '.env.production.example'
        ];
        
        requiredFiles.forEach(file => {
          if (!fs.existsSync(file)) {
            throw new Error(`الملف المطلوب غير موجود: ${file}`);
          }
        });
        
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }

  // فحص الأمان
  runSecurityCheck() {
    return new Promise((resolve, reject) => {
      try {
        colorLog('blue', 'تشغيل فحص الأمان...');
        execSync('node scripts/security-check.js', { stdio: 'inherit' });
        resolve();
      } catch (error) {
        reject(new Error('فشل في فحص الأمان. يجب إصلاح المشاكل الأمنية أولاً.'));
      }
    });
  }

  // تنظيف المشروع
  cleanProject() {
    return new Promise((resolve, reject) => {
      try {
        // حذف مجلدات البناء القديمة
        if (fs.existsSync('.next')) {
          execSync('rm -rf .next', { stdio: 'inherit' });
        }
        
        if (fs.existsSync('out')) {
          execSync('rm -rf out', { stdio: 'inherit' });
        }
        
        // حذف node_modules وإعادة التثبيت
        colorLog('blue', 'إعادة تثبيت التبعيات...');
        execSync('npm ci', { stdio: 'inherit' });
        
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }

  // تشغيل الاختبارات
  runTests() {
    return new Promise((resolve, reject) => {
      try {
        // تشغيل ESLint
        colorLog('blue', 'تشغيل ESLint...');
        execSync('npm run lint', { stdio: 'inherit' });
        
        // تشغيل فحص TypeScript
        colorLog('blue', 'تشغيل فحص TypeScript...');
        execSync('npx tsc --noEmit', { stdio: 'inherit' });
        
        resolve();
      } catch (error) {
        reject(new Error('فشل في الاختبارات. يجب إصلاح الأخطاء أولاً.'));
      }
    });
  }

  // بناء المشروع
  buildProject() {
    return new Promise((resolve, reject) => {
      try {
        colorLog('blue', 'بناء المشروع للإنتاج...');
        execSync('npm run build', { stdio: 'inherit' });
        resolve();
      } catch (error) {
        reject(new Error('فشل في بناء المشروع.'));
      }
    });
  }

  // إنشاء نسخة احتياطية
  createBackup() {
    return new Promise((resolve, reject) => {
      try {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupDir = `backup-${timestamp}`;
        
        colorLog('blue', `إنشاء نسخة احتياطية في ${backupDir}...`);
        
        // إنشاء مجلد النسخة الاحتياطية
        if (!fs.existsSync('backups')) {
          fs.mkdirSync('backups');
        }
        
        const backupPath = path.join('backups', backupDir);
        fs.mkdirSync(backupPath);
        
        // نسخ الملفات المهمة
        const importantFiles = [
          'data/encrypted-database.enc',
          'data/secure-admin.enc',
          'data/database-backup.json'
        ];
        
        importantFiles.forEach(file => {
          if (fs.existsSync(file)) {
            const fileName = path.basename(file);
            fs.copyFileSync(file, path.join(backupPath, fileName));
          }
        });
        
        colorLog('green', `تم إنشاء النسخة الاحتياطية: ${backupPath}`);
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }

  // تحسين الأداء
  optimizePerformance() {
    return new Promise((resolve, reject) => {
      try {
        colorLog('blue', 'تحسين الأداء...');
        
        // ضغط الملفات الثابتة
        if (fs.existsSync('.next/static')) {
          colorLog('blue', 'ضغط الملفات الثابتة...');
          // يمكن إضافة أدوات ضغط هنا
        }
        
        // تحسين الصور
        if (fs.existsSync('public')) {
          colorLog('blue', 'تحسين الصور...');
          // يمكن إضافة أدوات تحسين الصور هنا
        }
        
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }

  // التحقق النهائي
  finalVerification() {
    return new Promise((resolve, reject) => {
      try {
        colorLog('blue', 'التحقق النهائي...');
        
        // فحص وجود ملفات البناء
        if (!fs.existsSync('.next')) {
          throw new Error('مجلد البناء .next غير موجود');
        }
        
        // فحص حجم البناء
        const buildSize = execSync('du -sh .next', { encoding: 'utf8' }).trim();
        colorLog('blue', `حجم البناء: ${buildSize}`);
        
        // فحص الملفات المشفرة
        const encryptedFiles = [
          'data/encrypted-database.enc',
          'data/secure-admin.enc'
        ];
        
        encryptedFiles.forEach(file => {
          if (!fs.existsSync(file)) {
            throw new Error(`الملف المشفر غير موجود: ${file}`);
          }
        });
        
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }

  // إعداد خطوات النشر
  setupDeploymentSteps() {
    this.addStep('فحص المتطلبات الأساسية', () => this.checkPrerequisites());
    this.addStep('فحص الأمان', () => this.runSecurityCheck());
    this.addStep('إنشاء نسخة احتياطية', () => this.createBackup());
    this.addStep('تنظيف المشروع', () => this.cleanProject());
    this.addStep('تشغيل الاختبارات', () => this.runTests());
    this.addStep('بناء المشروع', () => this.buildProject());
    this.addStep('تحسين الأداء', () => this.optimizePerformance());
    this.addStep('التحقق النهائي', () => this.finalVerification());
  }
}

// تشغيل النشر
async function main() {
  const deployment = new SecureDeployment();
  deployment.setupDeploymentSteps();
  
  try {
    await deployment.deploy();
    
    colorLog('bold', '\n📋 خطوات ما بعد النشر:');
    colorLog('yellow', '1. ارفع الملفات إلى الخادم');
    colorLog('yellow', '2. تأكد من إعداد متغيرات البيئة في الخادم');
    colorLog('yellow', '3. قم بتشغيل المشروع: npm start');
    colorLog('yellow', '4. اختبر جميع الوظائف');
    colorLog('yellow', '5. راقب logs الأخطاء');
    
  } catch (error) {
    colorLog('red', `\n💥 فشل النشر: ${error.message}`);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = SecureDeployment;
