// سكريبت لاختبار اتصال SMTP مباشرة
const nodemailer = require('nodemailer');
const mysql = require('mysql2/promise');

async function testSMTP() {
  console.log('🧪 اختبار اتصال SMTP...\n');

  // إعدادات قاعدة البيانات
  const dbConfig = {
    host: 'localhost',
    port: 3306,
    user: 'root',
    password: '',
    database: 'droobhajer_db',
    charset: 'utf8mb4'
  };

  let connection;

  try {
    // الاتصال بقاعدة البيانات
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ تم الاتصال بقاعدة البيانات');

    // جلب إعدادات الإيميل
    const [rows] = await connection.execute('SELECT * FROM contact_info ORDER BY id DESC LIMIT 1');
    
    if (rows.length === 0) {
      console.log('❌ لا توجد إعدادات إيميل في قاعدة البيانات');
      return;
    }

    const contactInfo = rows[0];
    console.log('\n📊 إعدادات الإيميل:');
    console.log(`  📧 الإيميل: ${contactInfo.email}`);
    console.log(`  🔑 كلمة المرور: ${contactInfo.Password ? '***موجودة***' : 'غير موجودة'}`);
    console.log(`  🔑 كلمة المرور الفعلية: ${contactInfo.Password}`); // للتشخيص فقط
    console.log(`  🏠 المضيف: ${contactInfo.host}`);
    console.log(`  🔌 المنفذ: ${contactInfo.port}`);

    if (!contactInfo.email || !contactInfo.Password) {
      console.log('❌ بيانات الإيميل أو كلمة المرور مفقودة');
      return;
    }

    // إعدادات SMTP للاختبار - تجربة إعدادات مختلفة لـ Hostinger
    const smtpConfigs = [
      {
        name: 'Hostinger SSL (465)',
        host: 'smtp.hostinger.com',
        port: 465,
        secure: true,
        auth: {
          user: contactInfo.email,
          pass: contactInfo.Password
        },
        tls: {
          rejectUnauthorized: false,
          ciphers: 'SSLv3'
        }
      },
      {
        name: 'Hostinger TLS (587)',
        host: 'smtp.hostinger.com',
        port: 587,
        secure: false,
        auth: {
          user: contactInfo.email,
          pass: contactInfo.Password
        },
        tls: {
          rejectUnauthorized: false,
          starttls: {
            enable: true
          }
        }
      },
      {
        name: 'Hostinger Alternative (2525)',
        host: 'smtp.hostinger.com',
        port: 2525,
        secure: false,
        auth: {
          user: contactInfo.email,
          pass: contactInfo.Password
        },
        tls: {
          rejectUnauthorized: false
        }
      },
      {
        name: 'Gmail SMTP (للمقارنة)',
        host: 'smtp.gmail.com',
        port: 587,
        secure: false,
        auth: {
          user: contactInfo.email,
          pass: contactInfo.Password
        },
        tls: {
          rejectUnauthorized: false
        }
      }
    ];

    // اختبار كل إعداد
    for (let i = 0; i < smtpConfigs.length; i++) {
      const config = smtpConfigs[i];
      console.log(`\n🔄 اختبار ${config.name}:`);
      console.log(`  🏠 المضيف: ${config.host}`);
      console.log(`  🔌 المنفذ: ${config.port}`);
      console.log(`  🔒 آمن: ${config.secure ? 'نعم (SSL)' : 'لا (TLS)'}`);
      console.log(`  👤 المستخدم: ${config.auth.user}`);

      try {
        const transporter = nodemailer.createTransport(config);
        
        console.log('  🔄 جاري التحقق من الاتصال...');
        await transporter.verify();
        
        console.log('  ✅ نجح الاتصال!');
        
        // اختبار إرسال إيميل تجريبي
        console.log('  📧 جاري إرسال إيميل تجريبي...');
        
        const testEmail = {
          from: `"DROOB HAJER - اختبار" <${contactInfo.email}>`,
          to: contactInfo.email, // إرسال للنفس للاختبار
          subject: `🧪 اختبار SMTP - ${new Date().toLocaleString('ar-SA')}`,
          html: `
            <div dir="rtl" style="font-family: Arial, sans-serif; padding: 20px;">
              <h2>🧪 اختبار SMTP ناجح!</h2>
              <p>تم إرسال هذا الإيميل بنجاح باستخدام:</p>
              <ul>
                <li><strong>المضيف:</strong> ${config.host}</li>
                <li><strong>المنفذ:</strong> ${config.port}</li>
                <li><strong>التشفير:</strong> ${config.secure ? 'SSL' : 'TLS'}</li>
              </ul>
              <p><em>الوقت: ${new Date().toLocaleString('ar-SA')}</em></p>
            </div>
          `
        };

        const info = await transporter.sendMail(testEmail);
        console.log('  ✅ تم إرسال الإيميل بنجاح!');
        console.log(`  🆔 معرف الرسالة: ${info.messageId}`);
        console.log(`  📊 استجابة الخادم: ${info.response}`);
        
        // إذا نجح الإعداد الأول، لا حاجة لتجربة البقية
        break;
        
      } catch (error) {
        console.log('  ❌ فشل الاتصال:');
        console.log(`     الخطأ: ${error.message}`);
        
        if (error.code) {
          console.log(`     الكود: ${error.code}`);
        }
        
        if (error.command) {
          console.log(`     الأمر: ${error.command}`);
        }
        
        // تحليل نوع الخطأ
        if (error.message.includes('Invalid login') || error.message.includes('authentication failed')) {
          console.log('     💡 السبب المحتمل: خطأ في اسم المستخدم أو كلمة المرور');
        } else if (error.message.includes('ECONNECTION') || error.message.includes('ETIMEDOUT')) {
          console.log('     💡 السبب المحتمل: مشكلة في الاتصال بالخادم');
        } else if (error.message.includes('ENOTFOUND')) {
          console.log('     💡 السبب المحتمل: عنوان الخادم غير صحيح');
        }
      }
    }

  } catch (error) {
    console.error('❌ خطأ عام:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 تم إغلاق الاتصال بقاعدة البيانات');
    }
  }
}

// تشغيل الاختبار
testSMTP().catch(console.error);
