// أنواع البيانات لقاعدة البيانات MySQL

// جدول الفئات الرئيسية
export interface Category {
  id: string;
  name: string;
  name_ar: string;
  description?: string;
  description_ar?: string;
  image_url?: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
  deleted_at?: Date | null;
}

// جدول الفئات الفرعية
export interface Subcategory {
  id: string;
  name: string;
  name_ar: string;
  category_id: string;
  description?: string;
  description_ar?: string;
  image_url?: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
  deleted_at?: Date | null;
}

// جدول المنتجات
export interface Product {
  id: string;
  title: string;
  title_ar: string;
  description?: string;
  description_ar?: string;
  price: number;
  original_price?: number;
  is_available: boolean;
  category_id: string;
  subcategory_id: string;
  is_active: boolean;
  is_featured: boolean;
  created_at: Date;
  updated_at: Date;
  deleted_at?: Date | null;
}

// جدول صور المنتجات
export interface ProductImage {
  id: number;
  product_id: string;
  image_url: string;
  sort_order: number;
  created_at: Date;
}

// جدول مميزات المنتجات
export interface ProductFeature {
  id: number;
  product_id: string;
  feature_text: string;
  feature_text_ar: string;
  sort_order: number;
}

// جدول مواصفات المنتجات
export interface ProductSpecification {
  id: number;
  product_id: string;
  spec_key: string;
  spec_key_ar: string;
  spec_value: string;
  spec_value_ar: string;
  sort_order: number;
}

// جدول طلبات التسعير
export interface QuoteRequest {
  id: string;
  customer_name: string;
  customer_email: string;
  customer_phone: string;
  customer_company?: string;
  excel_file_url?: string;
  status: 'pending' | 'processed' | 'rejected';
  notes?: string;
  created_at: Date;
  updated_at: Date;
  deleted_at?: Date | null;
}

// جدول منتجات طلبات التسعير
export interface QuoteRequestProduct {
  id: number;
  quote_id: string;
  product_id: string;
  created_at: Date;
}

// جدول سجلات طلبات التسعير
export interface QuoteRequestLog {
  id: number;
  quote_id: string;
  action_by?: string;
  action_type: 'note' | 'status_change';
  note?: string;
  created_at: Date;
}

// جدول المديرين
export interface Admin {
  id: number;
  username: string;
  email: string;
  password_hash: string;
  is_active: boolean;
  last_login?: Date | null;
  created_at: Date;
  updated_at: Date;
  deleted_at?: Date | null;
}

// جدول معلومات الاتصال
export interface ContactInfo {
  id: number;
  email?: string;
  Password?: string;
  host: string;
  port: number;
  updated_at: Date;
}

// أنواع البيانات للإدخال (بدون الحقول المُولدة تلقائياً)
export type CategoryInput = Omit<Category, 'created_at' | 'updated_at' | 'deleted_at'>;
export type SubcategoryInput = Omit<Subcategory, 'created_at' | 'updated_at' | 'deleted_at'>;
export type ProductInput = Omit<Product, 'created_at' | 'updated_at' | 'deleted_at'>;
export type QuoteRequestInput = Omit<QuoteRequest, 'created_at' | 'updated_at' | 'deleted_at'>;
export type AdminInput = Omit<Admin, 'id' | 'created_at' | 'updated_at' | 'deleted_at' | 'last_login'>;

// أنواع البيانات للتحديث (جميع الحقول اختيارية ما عدا ID)
export type CategoryUpdate = Partial<Omit<Category, 'id' | 'created_at'>> & { updated_at?: Date };
export type SubcategoryUpdate = Partial<Omit<Subcategory, 'id' | 'created_at'>> & { updated_at?: Date };
export type ProductUpdate = Partial<Omit<Product, 'id' | 'created_at'>> & { updated_at?: Date };
export type QuoteRequestUpdate = Partial<Omit<QuoteRequest, 'id' | 'created_at'>> & { updated_at?: Date };
export type AdminUpdate = Partial<Omit<Admin, 'id' | 'created_at'>> & { updated_at?: Date };

// منتج مع البيانات المرتبطة
export interface ProductWithDetails extends Product {
  images: ProductImage[];
  features: ProductFeature[];
  specifications: ProductSpecification[];
  category?: Category;
  subcategory?: Subcategory;
}

// طلب تسعير مع البيانات المرتبطة
export interface QuoteRequestWithDetails extends QuoteRequest {
  products: QuoteRequestProduct[];
  logs: QuoteRequestLog[];
}
