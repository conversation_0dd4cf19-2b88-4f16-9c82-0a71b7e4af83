const mysql = require('mysql2/promise');

async function checkSubcategories() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root', 
      password: '',
      database: 'droobhajer_db'
    });
    
    console.log('=== Categories ===');
    const [categories] = await connection.execute('SELECT id, name, name_ar FROM categories WHERE deleted_at IS NULL AND is_active = 1');
    categories.forEach(cat => {
      console.log(`- ${cat.name_ar} (${cat.name}) - ID: ${cat.id}`);
    });
    
    console.log('\n=== Subcategories ===');
    const [subcategories] = await connection.execute('SELECT id, name, name_ar, category_id FROM subcategories WHERE deleted_at IS NULL AND is_active = 1');
    console.log(`عدد الفئات الفرعية: ${subcategories.length}`);
    subcategories.forEach(sub => {
      console.log(`- ${sub.name_ar} (${sub.name}) - Category ID: ${sub.category_id}`);
    });
    
    console.log('\n=== Categories with Subcategories Count ===');
    const [categoriesWithCount] = await connection.execute(`
      SELECT 
        c.id, 
        c.name, 
        c.name_ar,
        COUNT(s.id) as subcategory_count
      FROM categories c
      LEFT JOIN subcategories s ON c.id = s.category_id AND s.is_active = 1 AND s.deleted_at IS NULL
      WHERE c.is_active = 1 AND c.deleted_at IS NULL
      GROUP BY c.id, c.name, c.name_ar
      ORDER BY c.name_ar ASC
    `);
    
    categoriesWithCount.forEach(cat => {
      console.log(`- ${cat.name_ar}: ${cat.subcategory_count} فئة فرعية`);
    });
    
    await connection.end();
  } catch (error) {
    console.error('Error:', error.message);
  }
}

checkSubcategories();
